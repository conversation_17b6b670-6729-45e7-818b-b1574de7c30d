{"name": "print-gservetech", "version": "1.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"@auth/neon-adapter": "^1.9.0", "@auth/pg-adapter": "^1.9.1", "@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@react-google-maps/api": "^2.20.6", "@types/bcryptjs": "^2.4.6", "@types/google.maps": "^3.58.1", "@types/nodemailer": "^6.4.17", "@upstash/redis": "^1.34.9", "@vercel/blob": "^1.0.1", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formidable": "^3.5.4", "keen-slider": "^6.8.6", "konva": "^9.3.20", "lucide-react": "^0.507.0", "next": "^15.3.1", "next-auth": "^5.0.0-beta.27", "next-themes": "^0.4.6", "node-cache": "^5.1.2", "nodemailer": "^6.10.1", "perfect-freehand": "^1.2.2", "pg": "^8.16.0", "print-gservetech": "file:", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.2", "react-icons": "^5.5.0", "react-konva": "^19.0.3", "react-router-dom": "^7.6.1", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "together-ai": "^0.16.0", "uuid": "^11.1.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.11.30", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.2.9", "typescript": "^5"}}