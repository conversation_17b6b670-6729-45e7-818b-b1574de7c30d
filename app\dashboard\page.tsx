import { redirect } from "next/navigation"
import { auth } from "@/lib/auth" // Assuming you have auth configuration
import { AdminDashboard } from "@/components/admin-dashboard"
import { UserDashboard } from "@/components/user-dashboard"
import { cache } from "react"

// Cache the auth check to avoid repeated calls with a revalidation period
const getSession = cache(async () => {
  return await auth()
})

// Cache dashboard data fetching functions
const getDashboardOrders = cache(async () => {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const res = await fetch(`${baseUrl}/api/dashboard/orders`, {
      next: { revalidate: 1800 }, // Cache for 30 minutes
      cache: 'force-cache',
      headers: { 'Content-Type': 'application/json' }
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch orders: ${res.status}`);
    }

    const data = await res.json();
    return data;
  } catch (error) {
    console.error("Error fetching dashboard orders:", error);
    return { success: false, data: [] };
  }
})

const getDashboardProfile = cache(async () => {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const res = await fetch(`${baseUrl}/api/dashboard/profile`, {
      next: { revalidate: 1800 }, // Cache for 30 minutes
      cache: 'force-cache',
      headers: { 'Content-Type': 'application/json' }
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch profile: ${res.status}`);
    }

    const data = await res.json();
    return data;
  } catch (error) {
    console.error("Error fetching dashboard profile:", error);
    return { success: false, data: null };
  }
})

export default async function DashboardPage() {
  const session = await getSession()

  if (!session?.user) {
    redirect("/login")
  }

  // Check user role from session or database
  const userRole = session.user.userType || "user" // Assuming role is stored in session

  // Fetch initial dashboard data with caching
  const [ordersData, profileData] = await Promise.all([
    getDashboardOrders(),
    getDashboardProfile()
  ]);

  if (userRole === "admin") {
    return (
      <>
        <AdminDashboard initialOrders={ordersData} />
      </>
    )
  } else {
    return (
      <>
        <UserDashboard initialOrders={ordersData} initialProfile={profileData} />
      </>
    )
  }
}
