import { redirect } from "next/navigation"
import { auth } from "@/lib/auth" // Assuming you have auth configuration
import { AdminDashboard } from "@/components/admin-dashboard"
import { UserDashboard } from "@/components/user-dashboard"
import { cache } from "react"

// Cache the auth check to avoid repeated calls with a revalidation period
const getSession = cache(async () => {
  return await auth()
})

export default async function DashboardPage() {
  const session = await getSession()

  if (!session?.user) {
    redirect("/login")
  }

  // Check user role from session or database
  const userRole = session.user.userType || "user" // Assuming role is stored in session

  if (userRole === "admin") {
    return (
      <>
        <AdminDashboard />
      </>
    )
  } else {
    return (
      <>
        <UserDashboard />
      </>
    )
  }
}
