"use client"

import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Skeleton } from "@/components/ui/skeleton"
import {
  MapPin,
  Package,
  Truck,
  CreditCard,
  ShoppingBag,
  User,
  Mail,
  Phone,
  ChevronDown,
  ChevronUp,
  Edit,
  Save,
  X,
  ImageIcon,
  RefreshCw
} from "lucide-react"
import Image from "next/image"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
//import { Toast } from "@/components/ui/use-toast"
import { toast } from "sonner"
import { fetchOrders, fetchUserProfile } from "@/lib/cached-fetch"
import { revalidateDashboard } from "@/lib/actions"

interface OrderItem {
  id: string
  product_id: string
  product_name: string
  quantity: number
  price_at_order: number
  item_subtotal: number
  image_url?: string
  title?: string
  
}

interface Order {
  order_id: string
  order_number: string
  total_amount: number
  delivery_method: string
  delivery_fee: number
  promotional_discount: number
  status: string
  special_instructions: string | null
  created_at: string
  updated_at: string
  street: string
  city: string
  province: string
  postal_code: string
  country: string
  customer_name: string
  customer_email: string
  custom_design_image_url: string | null
  order_items: OrderItem[]
}

interface UserProfile {
  id: string
  name: string
  email: string
  phone: string
  street?: string
  city?: string
  province?: string
  postal_code?: string
  country?: string
}

const profileSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  email: z.string().email({
    message: "Please enter a valid email.",
  }),
  phone: z.string().min(6, {
    message: "Please enter a valid phone number.",
  }),
  street: z.string().optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),
})

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "pending":
      return "bg-yellow-100 text-yellow-800 border-yellow-200"
    case "confirmed":
      return "bg-blue-100 text-blue-800 border-blue-200"
    case "preparing":
      return "bg-orange-100 text-orange-800 border-orange-200"
    case "shipped":
    case "out_for_delivery":
      return "bg-purple-100 text-purple-800 border-purple-200"
    case "delivered":
      return "bg-green-100 text-green-800 border-green-200"
    case "cancelled":
      return "bg-red-100 text-red-800 border-red-200"
    default:
      return "bg-gray-100 text-gray-800 border-gray-200"
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount)
}

interface UserDashboardProps {
  initialOrders?: any;
  initialProfile?: any;
}

export function UserDashboard({ initialOrders, initialProfile }: UserDashboardProps) {
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(!initialOrders)
  const [profileLoading, setProfileLoading] = useState(!initialProfile)
  const [error, setError] = useState<string | null>(null)
  const [expandedOrder, setExpandedOrder] = useState<string | null>(null)
  const [editingProfile, setEditingProfile] = useState(false)
  const [userProfile, setUserProfile] = useState<UserProfile>({
    id: "",
    name: "",
    email: "",
    phone: "",
    street: "",
    city: "",
    province: "",
    postal_code: "",
    country: "",
  })

  const form = useForm<z.infer<typeof profileSchema>>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: userProfile.name,
      email: userProfile.email,
      phone: userProfile.phone,
      street: userProfile.street || "",
      city: userProfile.city || "",
      province: userProfile.province || "",
      postal_code: userProfile.postal_code || "",
      country: userProfile.country || "",
    },
  })

  useEffect(() => {
    if (initialOrders && initialProfile) {
      // Use initial data if available
      if (initialOrders.success) {
        setOrders(initialOrders.data || [])
        setLoading(false)
      }

      if (initialProfile.success && initialProfile.data) {
        setUserProfile(initialProfile.data)
        setProfileLoading(false)
      }
    } else {
      // Fallback to fetching data
      fetchDashboardData()
    }
  }, [initialOrders, initialProfile])

  useEffect(() => {
    if (userProfile) {
      form.reset({
        name: userProfile.name,
        email: userProfile.email,
        phone: userProfile.phone,
        street: userProfile.street || "",
        city: userProfile.city || "",
        province: userProfile.province || "",
        postal_code: userProfile.postal_code || "",
        country: userProfile.country || "",
      })
    }
  }, [userProfile, form])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      setProfileLoading(true)
      setError(null)

      // Use cached fetch functions
      const ordersData = await fetchOrders()
      const profileData = await fetchUserProfile()

      if (ordersData.success) {
        setOrders(ordersData.data || [])
      } else {
        throw new Error(ordersData.error || "Failed to fetch orders")
      }

      if (profileData.success && profileData.data) {
        setUserProfile(profileData.data)
      }
    } catch (err) {
      console.error("Fetch Dashboard Error:", err)
      setError(err instanceof Error ? err.message : "An error occurred while fetching dashboard data")
    } finally {
      setLoading(false)
      setProfileLoading(false)
    }
  }

  const updateUserProfile = async (values: z.infer<typeof profileSchema>) => {
    try {
      const response = await fetch('/api/dashboard/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(values)
      })

      if (!response.ok) {
        throw new Error('Failed to update profile')
      }

      const data = await response.json()
      if (data.success) {
        setUserProfile(data.data);
        toast.success("Profile updated successfully");
        setEditingProfile(false);

      }
    } catch (err) {
      console.error("Fetch Profile Error:", err);
      toast.error("Failed to update profile information");

    }
  }

  const toggleOrderDetails = (orderId: string) => {
    setExpandedOrder(expandedOrder === orderId ? null : orderId)
  }

  // Function to refresh data by triggering revalidation
  const refreshData = async () => {
    try {
      setLoading(true);
      toast.info("Refreshing dashboard data...");
      
      // Trigger server-side revalidation
      await revalidateDashboard();
      
      // Fetch fresh data
      await fetchDashboardData();
      
      toast.success("Dashboard data refreshed");
    } catch (error) {
      console.error("Error refreshing data:", error);
      toast.error("Failed to refresh data");
    } finally {
      setLoading(false);
    }
  };

  if (loading || profileLoading) {
    return (
      <div className="container mx-auto p-4 space-y-6">
        {/* Profile Skeleton */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-4">
              <Skeleton className="h-16 w-16 rounded-full" />
              <div className="space-y-2">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-36" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Skeleton */}
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-16 w-16 rounded-md" />
                    <div className="space-y-2">
                      <Skeleton className="h-5 w-32" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                  </div>
                  <div className="text-right space-y-2">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <p className="text-red-600 font-medium">Error loading orders</p>
              <p className="text-red-500 text-sm">{error}</p>
              <Button onClick={fetchDashboardData} variant="outline" size="sm">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">My Dashboard</h1>
        <Button onClick={refreshData} variant="outline" size="sm" disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
          Refresh Data
        </Button>
      </div>

      {/* User Profile Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Profile
            {!editingProfile && (
              <Button
                variant="ghost"
                size="sm"
                className="ml-auto"
                onClick={() => setEditingProfile(true)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {editingProfile ? (
            <form onSubmit={form.handleSubmit(updateUserProfile)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input id="name" {...form.register("name")} />
                  {form.formState.errors.name && (
                    <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" {...form.register("email")} />
                  {form.formState.errors.email && (
                    <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input id="phone" {...form.register("phone")} />
                  {form.formState.errors.phone && (
                    <p className="text-sm text-red-500">{form.formState.errors.phone.message}</p>
                  )}
                </div>
              </div>

              <Separator />

              <h4 className="font-medium flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Address
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="street">Street</Label>
                  <Input id="street" {...form.register("street")} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input id="city" {...form.register("city")} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="province">State/Province</Label>
                  <Input id="province" {...form.register("province")} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="postal_code">Postal Code</Label>
                  <Input id="postal_code" {...form.register("postal_code")} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input id="country" {...form.register("country")} />
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setEditingProfile(false)}
                  type="button"
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button type="submit">
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </div>
            </form>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center border-2 border-primary/20">
                  <div className="text-lg font-semibold text-primary">
                    {userProfile.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold">{userProfile.name}</h3>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Mail className="h-4 w-4" />
                    {userProfile.email}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Phone className="h-4 w-4" />
                    {userProfile.phone}
                  </div>
                </div>
              </div>

              {(userProfile.street || userProfile.city || userProfile.province || userProfile.postal_code || userProfile.country) && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h4 className="font-medium flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Address
                    </h4>
                    <div className="text-sm text-muted-foreground">
                      {userProfile.street && <p>{userProfile.street}</p>}
                      {(userProfile.city || userProfile.province || userProfile.postal_code) && (
                        <p>
                          {userProfile.city}{userProfile.city && userProfile.province ? ', ' : ''}
                          {userProfile.province} {userProfile.postal_code}
                        </p>
                      )}
                      {userProfile.country && <p>{userProfile.country}</p>}
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Orders Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            My Orders
          </CardTitle>
        </CardHeader>
        <CardContent>
          {orders.length === 0 ? (
            <div className="text-center space-y-4">
              <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground" />
              <div className="space-y-2">
                <h3 className="font-medium">No orders yet</h3>
                <p className="text-sm text-muted-foreground">When you place your first order, it will appear here.</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {orders.map((order) => (
                <Card key={order.order_id} className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow">
                  {/* Compact Order Card */}
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between" onClick={() => toggleOrderDetails(order.order_id)}>
                      <div className="flex items-center space-x-4">
                        {/* First Product Image */}
                        <div className="relative h-16 w-16 rounded-md overflow-hidden bg-background border">
                          {order.order_items[0]?.image_url ? (
                            <Image
                              src={order.order_items[0].image_url || "/placeholder.svg"}
                              alt={order.order_items[0].title || order.order_items[0].product_name}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <div className="h-full w-full flex items-center justify-center">
                              <Package className="h-8 w-8 text-muted-foreground" />
                            </div>
                          )}
              

                          {order.order_items.length > 1 && (
                            <div className="absolute -bottom-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-6 w-6 flex items-center justify-center">
                              +{order.order_items.length - 1}
                            </div>
                          )}
                        </div>

                        {/* Order Info */}
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold">#{order.order_number}</h3>
                            <Badge className={getStatusColor(order.status)}>
                              {order.status.replace("_", " ").toUpperCase()}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {order.order_items[0]?.title || order.order_items[0]?.product_name}
                            {order.order_items.length > 1 && ` and ${order.order_items.length - 1} more items`}
                          </p>
                          <p className="text-xs text-muted-foreground">{formatDate(order.created_at)}</p>
                        </div>
                      </div>

                      {/* Order Summary */}
                      <div className="text-right space-y-1">
                        <p className="font-semibold text-lg">{formatCurrency(order.total_amount)}</p>
                        <p className="text-sm text-muted-foreground">
                          {order.order_items.reduce((sum, item) => sum + item.quantity, 0)} items
                        </p>
                        <div className="flex items-center gap-1 text-muted-foreground">
                          {expandedOrder === order.order_id ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Expanded Order Details */}
                    {expandedOrder === order.order_id && (
                      <div className="mt-6 space-y-4 border-t pt-4">
                        {/* Order Items */}
                        <div className="space-y-3">
                          <h4 className="font-medium flex items-center gap-2">
                            <Package className="h-4 w-4" />
                            Items Ordered
                          </h4>
                          <div className="grid gap-3">
                            {order.order_items.map((item) => (
                              <div key={item.id}>
                                {/* Main Item Row */}
                                <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                                  <div className="relative h-12 w-12 rounded-md overflow-hidden bg-background border">
                                    {item.image_url ? (
                                      <Image
                                        src={item.image_url || "/placeholder.svg"}
                                        alt={item.title || item.product_name}
                                        fill
                                        className="object-cover"
                                      />
                                    ) : (
                                      <div className="h-full w-full flex items-center justify-center">
                                        <Package className="h-6 w-6 text-muted-foreground" />
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <p className="font-medium text-sm truncate">{item.title || item.product_name}</p>
                                    <p className="text-xs text-muted-foreground">
                                      Qty: {item.quantity} × {formatCurrency(item.price_at_order)}
                                    </p>
                                  </div>
                                  <div className="text-right">
                                    <p className="font-medium text-sm">{formatCurrency(item.item_subtotal)}</p>
                                  </div>
                                </div>

                                

                              </div>
                            ))}
                          </div>
                        </div>

                        <Separator />

                        {/* Order Details Grid */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* Delivery Information */}
                          <div className="space-y-3">
                            <h4 className="font-medium flex items-center gap-2">
                              <Truck className="h-4 w-4" />
                              Delivery Details
                            </h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex items-start gap-2">
                                <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                                <div>
                                  <p>{order.street}</p>
                                  <p>
                                    {order.city}, {order.province} {order.postal_code}
                                  </p>
                                  <p>{order.country}</p>
                                </div>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Method:</span>
                                <span className="capitalize">{order.delivery_method.replace("_", " ")}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Delivery Fee:</span>
                                <span>{formatCurrency(order.delivery_fee)}</span>
                              </div>
                            </div>
                          </div>

                          {/* Payment Summary */}
                          <div className="space-y-3">
                            <h4 className="font-medium flex items-center gap-2">
                              <CreditCard className="h-4 w-4" />
                              Payment Summary
                            </h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Subtotal:</span>
                                <span>
                                  {formatCurrency(order.total_amount - order.delivery_fee + order.promotional_discount)}
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-muted-foreground">Delivery:</span>
                                <span>{formatCurrency(order.delivery_fee)}</span>
                              </div>
                              {order.promotional_discount > 0 && (
                                <div className="flex justify-between text-green-600">
                                  <span>Discount:</span>
                                  <span>-{formatCurrency(order.promotional_discount)}</span>
                                </div>
                              )}
                              <Separator />
                              <div className="flex justify-between font-medium">
                                <span>Total:</span>
                                <span>{formatCurrency(order.total_amount)}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Special Instructions */}
                        {order.special_instructions && (
                          <>
                            <Separator />
                            <div className="space-y-2">
                              <h4 className="font-medium text-sm">Special Instructions</h4>
                              <p className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg">
                                {order.special_instructions}
                              </p>
                            </div>
                          </>
                        )}

                        {/* Custom Design Section - Separate and prominent */}
                        {order.custom_design_image_url && (
                          <>
                            <Separator className="my-4" />
                            <div className="space-y-3">
                              <h4 className="font-medium flex items-center gap-2">
                                <ImageIcon className="h-4 w-4" />
                                Custom Design
                              </h4>
                              <div className="flex flex-col items-center gap-4 p-4 bg-muted/50 rounded-lg">
                                <div className="relative w-full max-w-lg aspect-square rounded-lg overflow-hidden border">
                                  <Image
                                    src={order.custom_design_image_url}
                                    alt={`Custom design for order #${order.order_number}`}
                                    fill
                                    className="object-contain"
                                  />
                                </div>
                                <p className="text-sm text-muted-foreground text-center">
                                  This is your custom design created for this order
                                </p>
                              </div>
                            </div>
                          </>
                        )}

                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {error && (
        <div className="p-4 bg-red-50 border border-red-100 rounded-lg text-center space-y-2">
          <p className="text-red-600 font-medium">Error loading orders</p>
          <p className="text-red-500 text-sm">{error}</p>
          <Button onClick={fetchDashboardData} variant="outline" size="sm">
            Try Again
          </Button>
        </div>
      )}
    </div>
  )
}

