import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { put } from '@vercel/blob'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric',
  });
}

export async function uploadBase64ImageToBlob(base64Image: string, fileName: string): Promise<string> {
  try {
    // Validate input
    if (!base64Image || !fileName) {
      throw new Error('Missing base64Image or fileName');
    }

    if (!base64Image.includes(',')) {
      throw new Error('Invalid base64 image format');
    }

    console.log(`Starting blob upload for file: ${fileName}`);

    // Extract the base64 data from the data URL
    const base64Data = base64Image.split(',')[1];

    if (!base64Data) {
      throw new Error('No base64 data found in image');
    }

    // Convert base64 to buffer
    const buffer = Buffer.from(base64Data, 'base64');

    // Validate buffer size (limit to 10MB)
    if (buffer.length > 10 * 1024 * 1024) {
      throw new Error('Image too large (max 10MB)');
    }

    console.log(`Image buffer size: ${buffer.length} bytes`);

    // Create a Blob from the buffer
    const blob = new Blob([buffer]);

    // Add timeout to blob upload
    const uploadPromise = put(fileName, blob, {
      access: 'public',
    });

    // Set a timeout for blob upload (60 seconds)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Blob upload timeout')), 60000);
    });

    const { url } = await Promise.race([uploadPromise, timeoutPromise]) as { url: string };

    console.log(`Successfully uploaded to blob: ${url}`);
    return url;
  } catch (error) {
    console.error('Error uploading image to Vercel Blob:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        throw new Error('Upload timeout - please try again');
      }
      if (error.message.includes('too large')) {
        throw error; // Pass through size errors
      }
    }

    throw new Error('Failed to upload image');
  }
}
