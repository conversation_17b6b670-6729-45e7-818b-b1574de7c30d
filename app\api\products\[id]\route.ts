import { safeQuery } from "@/lib/db"
import { NextResponse } from "next/server"
import NodeCache from "node-cache"

// Cache for individual products (30 minutes TTL)
const productCache = new NodeCache({
  stdTTL: 1800, // 30 minutes
  checkperiod: 300, // Check for expired keys every 5 minutes
  useClones: false, // Don't clone objects for better performance
  maxKeys: 500 // Limit cache size
})

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  
  // Check cache first
  const cacheKey = `product-${id}`
  const cachedProduct = productCache.get(cacheKey)
  
  if (cachedProduct) {
    return NextResponse.json(cachedProduct, {
      headers: {
        'Cache-Control': 'max-age=1800, s-maxage=1800', // 30 minutes
      }
    })
  }
  
  try {
    console.log('Fetching product:', id);

    const result = await safeQuery(
      `SELECT
        id,
        title,
        short_description,
        long_description,
        price,
        promotional_price_percent,
        sales_price_percent,
        image_url,
        gallery_urls,
        subcategory_id,
        is_featured
      FROM product
      WHERE id = $1`,
      [id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    const product = result.rows[0]
    console.log('Product fetched successfully');
    
    // Store in cache
    productCache.set(cacheKey, product)
    
    return NextResponse.json(product, {
      headers: {
        'Cache-Control': 'max-age=1800, s-maxage=1800', // 30 minutes
      }
    })
  } catch (error) {
    console.error("Fetch product error:", error)

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
        console.error('Database connection limit reached in product fetch');
        return NextResponse.json({ error: "Service temporarily unavailable. Please try again." }, { status: 503 });
      }
    }

    return NextResponse.json({ error: "Failed to fetch product" }, { status: 500 })
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const body = await request.json()
  try {
    console.log('Updating product:', id);

    const result = await safeQuery(
      `UPDATE product SET
        title = $1,
        short_description = $2,
        long_description = $3,
        price = $4,
        promotional_price_percent = $5,
        sales_price_percent = $6,
        image_url = $7,
        gallery_urls = $8,
        subcategory_id = $9,
        is_featured = $10
      WHERE id = $11
      RETURNING *`,
      [
        body.title,
        body.short_description,
        body.long_description,
        body.price,
        body.promotional_price_percent,
        body.sales_price_percent,
        body.image_url,
        body.gallery_urls,
        body.subcategory_id,
        body.is_featured,
        id,
      ]
    )
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    console.log('Product updated successfully');
    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error("Update product error:", error)

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
        console.error('Database connection limit reached in product update');
        return NextResponse.json({ error: "Service temporarily unavailable. Please try again." }, { status: 503 });
      }
    }

    return NextResponse.json({ error: "Failed to update product" }, { status: 500 })
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  try {
    console.log('Deleting product:', id);

    const result = await safeQuery(
      "DELETE FROM product WHERE id = $1 RETURNING *",
      [id]
    )
    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }

    console.log('Product deleted successfully');
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Delete product error:", error)

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
        console.error('Database connection limit reached in product delete');
        return NextResponse.json({ error: "Service temporarily unavailable. Please try again." }, { status: 503 });
      }
    }

    return NextResponse.json({ error: "Failed to delete product" }, { status: 500 })
  }
}