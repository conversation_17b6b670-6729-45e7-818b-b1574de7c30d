import { NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { getPool } from '@/lib/db';
import { dbCircuitBreaker } from '@/lib/db-circuit-breaker';

export async function GET() {
  try {
    const session = await auth();
    
    // Only allow admins to access connection debug info
    if (!session?.user?.id || session.user.userType !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const pool = getPool();
    const circuitBreakerStatus = dbCircuitBreaker.getStatus();
    
    // Get pool statistics
    const poolStats = {
      totalCount: pool.totalCount,
      idleCount: pool.idleCount,
      waitingCount: pool.waitingCount,
      maxConnections: pool.options.max || 'unknown'
    };

    // Try to get database connection info
    let dbConnectionInfo = null;
    try {
      if (circuitBreakerStatus.canExecute) {
        const result = await pool.query(`
          SELECT 
            state,
            COUNT(*) as count,
            (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_conn
          FROM pg_stat_activity 
          WHERE datname = current_database()
          GROUP BY state, max_conn
        `);

        const connectionsByState: Record<string, number> = {};
        let totalConnections = 0;
        let maxConnections = 100;

        result.rows.forEach(row => {
          connectionsByState[row.state] = row.count;
          totalConnections += row.count;
          maxConnections = row.max_conn;
        });

        dbConnectionInfo = {
          totalConnections,
          maxConnections,
          connectionsByState,
          utilizationPercent: Math.round((totalConnections / maxConnections) * 100)
        };
      }
    } catch (error) {
      console.error('Failed to get database connection info:', error);
    }

    // Add recommendations based on current state
    const recommendations: string[] = [];

    const debugInfo = {
      timestamp: new Date().toISOString(),
      poolStats,
      circuitBreaker: circuitBreakerStatus,
      database: dbConnectionInfo,
      recommendations: recommendations
    };
    
    if (poolStats.waitingCount > 0) {
      recommendations.push(`${poolStats.waitingCount} clients waiting for connections - consider optimizing queries`);
    }
    
    if (dbConnectionInfo && dbConnectionInfo.utilizationPercent > 80) {
      recommendations.push(`Database connection usage is ${dbConnectionInfo.utilizationPercent}% - monitor for potential issues`);
    }
    
    if (circuitBreakerStatus.state !== 'CLOSED') {
      recommendations.push(`Circuit breaker is ${circuitBreakerStatus.state} - database operations may be limited`);
    }
    
    if (circuitBreakerStatus.failures > 0) {
      recommendations.push(`${circuitBreakerStatus.failures} recent connection failures detected`);
    }

    return NextResponse.json(debugInfo);
  } catch (error) {
    console.error('Connection debug error:', error);
    
    return NextResponse.json({
      error: 'Failed to get connection debug info',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
