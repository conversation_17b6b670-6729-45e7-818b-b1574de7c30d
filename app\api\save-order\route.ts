import fs from 'fs';
import path from 'path';
import { NextResponse } from 'next/server';
import { Order } from '../../../types';

export async function POST(request: Request) {
  try {
    const order: Order = await request.json();
    const filePath = path.join(process.cwd(), 'data', 'orders.json');

    // Ensure data folder exists
    if (!fs.existsSync(path.dirname(filePath))) {
      fs.mkdirSync(path.dirname(filePath));
    }

    let orders: Order[] = [];
    if (fs.existsSync(filePath)) {
      const jsonData = fs.readFileSync(filePath);
      orders = JSON.parse(jsonData.toString());
    }

    orders.push(order);
    fs.writeFileSync(filePath, JSON.stringify(orders, null, 2));

    return NextResponse.json({ message: 'Order saved successfully' }, { status: 200 });
  } catch (error) {
    console.error('Error saving order:', error);
    return NextResponse.json({ error: 'Failed to save order' }, { status: 500 });
  }
} 