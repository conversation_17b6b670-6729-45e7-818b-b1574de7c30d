import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, ShoppingCart, Star } from "lucide-react";

export function ProductDetailSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-emerald-50">
      <div className="container mx-auto px-4 md:px-8 py-12">
        <div className="flex flex-col md:flex-row gap-12">
          {/* Left: Images */}
          <div className="md:w-1/2 flex flex-col gap-6">
            <div className="relative w-full h-[400px] rounded-xl overflow-hidden shadow-lg border border-emerald-200 bg-white">
              <Skeleton className="h-full w-full" />
            </div>
            <div className="flex gap-3">
              {Array(4).fill(0).map((_, i) => (
                <div
                  key={i}
                  className="relative w-20 h-20 rounded-lg overflow-hidden border-2 border-gray-200"
                >
                  <Skeleton className="h-full w-full" />
                </div>
              ))}
            </div>
          </div>
          {/* Right: Details */}
          <div className="md:w-1/2 flex flex-col gap-8">
            <div>
              <div className="flex items-center gap-1.5 px-3 py-1.5 text-sm bg-white text-green-700 border border-green-600 rounded-md w-fit mb-4">
                <ArrowLeft className="h-4 w-4" />
                <Skeleton className="h-4 w-24" />
              </div>
              <Skeleton className="h-10 w-3/4 mb-2" />
              <Skeleton className="h-6 w-full mb-4" />
              <div className="flex items-center gap-2 text-sm">
                <Skeleton className="h-4 w-10" />
                <Skeleton className="h-4 w-40" />
              </div>
            </div>
            <div>
              <Skeleton className="h-6 w-40 mb-2" />
              <ul className="space-y-2 text-sm">
                {Array(4).fill(0).map((_, i) => (
                  <li key={i} className="flex items-start">
                    <span className="bg-emerald-100 rounded-full p-1 mr-2 flex-shrink-0">
                      <Star className="h-3 w-3 text-emerald-600" />
                    </span>
                    <Skeleton className="h-4 w-full" />
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <Skeleton className="h-6 w-32 mb-2" />
              <Skeleton className="h-4 w-full mb-1" />
              <Skeleton className="h-4 w-full mb-1" />
              <Skeleton className="h-4 w-3/4" />
            </div>
            <div>
              <Skeleton className="h-6 w-24 mb-2" />
              <Skeleton className="h-10 w-40 mb-1" />
              <Skeleton className="h-4 w-56" />
            </div>
            <Button
              disabled
              size="lg"
              className="w-full bg-emerald-600 opacity-70 mt-6"
            >
              <ShoppingCart className="mr-2 h-5 w-5" />
              Buy Now
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
} 