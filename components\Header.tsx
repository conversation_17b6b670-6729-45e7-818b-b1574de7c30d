"use client"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Clock, CreditCard, Mail, Menu, Phone, Truck, User, X } from "lucide-react"
import { signOut, useSession } from "next-auth/react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"

const Header = () => {
  const { data: session } = useSession()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const navItems = [
    { name: "Home", path: "/" },
    { name: "Products", path: "/products", hasDropdown: true },
    { name: "Contact Us", path: "/contact" },
  ]
  
  const isAdmin = () => {
    if (!session?.user?.email) return false
    const adminEmails = [
      '<EMAIL>'
    ]
    return adminEmails.includes(session.user.email)
  }

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  return (
    <header className="w-full">
       <div className="bg-blue-600 text-white py-2 overflow-hidden hidden md:block">
        <div className="animate-marquee whitespace-nowrap">
          <div className="inline-flex items-center space-x-8">
            <div className="flex items-center">
              <Phone className="h-4 w-4 mr-2" />
              <span>Online Orders: {process.env.NEXT_PUBLIC_PHONE_NO}</span>
            </div>
            <div className="flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              <span>Email: {process.env.NEXT_PUBLIC_EMAIL}</span>
            </div>
            <div className="flex items-center">
              <Truck className="h-4 w-4 mr-2" />
              <span>Free shipping on orders over $149</span>
            </div>
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              <span>24/7 Online Ordering</span>
            </div>
            <div className="flex items-center">
              <CreditCard className="h-4 w-4 mr-2" />
              <span>Secure Online Payments</span>
            </div>
          </div>
        </div>
      </div>
      {/* <div className="bg-blue-600 text-white py-2 text-center">
        <p className="text-sm font-medium">PrintCloud is a Proudly Canadian Company</p>
      </div> */}

      <div className="bg-gray-900 text-white py-4">
        <div className="container mx-auto px-4 flex justify-between items-center">
          <Link href="/" prefetch={true} className="relative w-32 md:w-40 h-10 md:h-12 flex items-center">
            <Image src="/images/logo.png" alt="PrintCloud Logo" width={160} height={48} className="object-contain" />
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-4">
            {navItems.map((item) => (
              <Link key={item.name} href={item.path} prefetch={true} className="text-sm font-medium px-4 py-2 hover:text-blue-300">
                {item.name}
              </Link>
            ))}
            {isAdmin() && (
              <Link href="/admin/manage-products" prefetch={true} className="text-sm font-medium px-4 py-2 text-white hover:text-blue-300">
                Manage Products
              </Link>
            )}
            {session && (
             <Link href="/dashboard" prefetch={true} className="text-sm font-medium px-4 py-2 text-white hover:text-blue-300">
                My Dashboard
             </Link>
             )}

          </div>

          {/* User Menu - Desktop */}
          <div className="hidden md:flex items-center space-x-2">
            {session ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-white">
                    <User className="h-5 w-5 mr-1" />
                    <span>{session.user?.name?.split(" ")[0] || "User"}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/orders" prefetch={true}>My Orders</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => signOut()}>Sign Out</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Link href="/login" prefetch={true}>
                <Button variant="ghost" size="sm" className="text-white">
                  <User className="h-5 w-5 mr-1" /> Sign In
                </Button>
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center">
            <Button variant="ghost" size="sm" onClick={toggleMobileMenu} className="text-white p-1">
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation Menu */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-gray-800 py-4 px-4 absolute w-full z-50">
            <nav className="flex flex-col space-y-3">
              {navItems.map((item) => (
                <Link 
                  key={item.name} 
                  href={item.path} 
                  prefetch={true} 
                  className="text-sm font-medium py-2 hover:text-blue-300 border-b border-gray-700"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              {isAdmin() && (
                <Link 
                  href="/admin/manage-products" 
                  prefetch={true} 
                  className="text-sm font-medium py-2 text-white hover:text-blue-300 border-b border-gray-700"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Manage Products
                </Link>
              )}
             {session ? (
  <>
    <Link 
      href="/dashboard" 
      prefetch={true} 
      className="text-sm font-medium py-2 hover:text-blue-300 border-b border-gray-700"
      onClick={() => setMobileMenuOpen(false)}
    >
      My Dashboard
    </Link>
    <Link 
      href="/orders" 
      prefetch={true} 
      className="text-sm font-medium py-2 hover:text-blue-300 border-b border-gray-700"
      onClick={() => setMobileMenuOpen(false)}
    >
      My Orders
    </Link>
    <button 
      onClick={() => {
        signOut();
        setMobileMenuOpen(false);
      }} 
      className="text-sm font-medium py-2 text-left hover:text-blue-300"
    >
      Sign Out
    </button>
  </>
) : (

                <Link 
                  href="/login" 
                  prefetch={true} 
                  className="text-sm font-medium py-2 hover:text-blue-300 flex items-center"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <User className="h-4 w-4 mr-2" /> Sign In
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
