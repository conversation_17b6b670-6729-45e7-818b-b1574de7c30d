"use client";

import { useEffect, useState, type ChangeEvent, type FormEvent } from "react";
import Image from "next/image";
import { Check, Save, Package, Plus, Upload } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import AdminProtected from "@/components/AdminProtected";
import { toast } from "sonner";
import { useRouter } from "next/navigation";


type ProductForm = {
  title: string[];
  shortDesc: string[];
  longDesc: string[];
  price: string;
  promotionalPricePercent: string;
  salesPricePercent: string;
  image: File | null;
  galleryImages: File[];
  isFeatured?: boolean; // <-- add this
};

type Category = { id: number; name: string };
type Subcategory = { id: number; name: string };

function AddProductPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedSubcategory, setSelectedSubcategory] = useState("");
  const [mainImagePreview, setMainImagePreview] = useState<string | null>(null);
  const [galleryPreviews, setGalleryPreviews] = useState<string[]>([]);
  const [form, setForm] = useState<ProductForm>({
    title: [""],
    shortDesc: [""],
    longDesc: [""],
    price: "",
    promotionalPricePercent: "",
    salesPricePercent: "",
    image: null,
    galleryImages: [],
    isFeatured: false, // <-- default value
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Fetch categories on mount
  useEffect(() => {
    fetch("/api/categories")
      .then(res => res.json())
      .then(data => setCategories(data.categories));
  }, []);

  // Fetch subcategories when category changes
  useEffect(() => {
    if (selectedCategory) {
      fetch(`/api/subcategories?categoryId=${selectedCategory}`)
        .then(res => res.json())
        .then(data => setSubcategories(data.subcategories));
    } else {
      setSubcategories([]);
    }
  }, [selectedCategory]);

  // Handle array fields (for multi-language or variations)
  const handleArrayChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field: "title" | "shortDesc" | "longDesc",
    idx: number
  ) => {
    const value = e.target.value;
    setForm(prev => {
      const arr = [...prev[field]];
      arr[idx] = value;
      return { ...prev, [field]: arr };
    });
  };

  // Add new entry for array fields
  const handleAddArrayField = (field: "title" | "shortDesc" | "longDesc") => {
    setForm(prev => ({
      ...prev,
      [field]: [...prev[field], ""],
    }));
  };

  // Remove entry for array fields
  const handleRemoveArrayField = (field: "title" | "shortDesc" | "longDesc", idx: number) => {
    setForm(prev => {
      const arr = [...prev[field]];
      arr.splice(idx, 1);
      return { ...prev, [field]: arr.length ? arr : [""] };
    });
  };

  // Handle other fields
  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    if (e.target.type === "file") {
      const fileInput = e.target as HTMLInputElement;
      const files = fileInput.files;

      if (name === "image" && files && files.length > 0) {
        setForm({ ...form, image: files[0] });

        // Preview for main image
        const reader = new FileReader();
        reader.onload = () => setMainImagePreview(reader.result as string);
        reader.readAsDataURL(files[0]);
      } else if (name === "galleryImages" && files) {
        const newGalleryImages = Array.from(files);
        const updatedGalleryImages = [...form.galleryImages, ...newGalleryImages];
        setForm({ ...form, galleryImages: updatedGalleryImages });

        Promise.all(
          newGalleryImages.map(
            file =>
              new Promise<string>((resolve) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result as string);
                reader.readAsDataURL(file);
              })
          )
        ).then(newPreviews => setGalleryPreviews(prev => [...prev, ...newPreviews]));
      }
    } else {
      setForm({ ...form, [name]: value });
    }
  };

  // Handle checkbox
  const handleCheckboxChange = (e: ChangeEvent<HTMLInputElement>) => {
    setForm(prev => ({
      ...prev,
      isFeatured: e.target.checked,
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form data
      if (!form.title[0].trim()) {
        throw new Error("Product title is required");
      }
      
      if (!form.shortDesc[0].trim()) {
        throw new Error("Short description is required");
      }
      
      if (!form.longDesc[0].trim()) {
        throw new Error("Product description is required");
      }
      
      if (!form.price || isNaN(Number(form.price))) {
        throw new Error("Valid price is required");
      }
      
      if (!selectedCategory) {
        throw new Error("Please select a category");
      }
      
      if (!selectedSubcategory) {
        throw new Error("Please select a subcategory");
      }

      // 1. Upload main image to Vercel Blob
      let imageUrl = "";
      if (form.image) {
        try {
          const imgData = new FormData();
          imgData.append("file", form.image);
          const imgRes = await fetch("/api/upload-to-blob", { method: "POST", body: imgData });
          
          if (!imgRes.ok) {
            throw new Error(`Image upload failed: ${imgRes.status}`);
          }
          
          const imgJson = await imgRes.json();
          imageUrl = imgJson.url;
        } catch (error) {
          console.error("Error uploading main image:", error);
          throw new Error("Failed to upload main product image. Please try again.");
        }
      } else {
        // Use a default placeholder if no image is provided
        imageUrl = "/placeholder.svg";
      }

      // 2. Upload gallery images to Vercel Blob
      const galleryUrls: string[] = [];
      for (const file of form.galleryImages) {
        try {
          const gData = new FormData();
          gData.append("file", file);
          const gRes = await fetch("/api/upload-to-blob", { method: "POST", body: gData });
          
          if (!gRes.ok) {
            throw new Error(`Gallery image upload failed: ${gRes.status}`);
          }
          
          const gJson = await gRes.json();
          galleryUrls.push(gJson.url);
        } catch (error) {
          console.error("Error uploading gallery image:", error);
          // Continue with other images if one fails
        }
      }

      // Prepare numeric values - convert empty strings to null
      const price = Number(form.price);
      const promotionalPricePercent = form.promotionalPricePercent ? Number(form.promotionalPricePercent) : null;
      const salesPricePercent = form.salesPricePercent ? Number(form.salesPricePercent) : null;

      // 3. Save product to Neon DB
      const res = await fetch("/api/addProduct", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: form.title,
          shortDesc: form.shortDesc,
          longDesc: form.longDesc,
          price,
          promotionalPricePercent,
          salesPricePercent,
          categoryId: selectedCategory,
          subcategoryId: selectedSubcategory,
          imageUrl,
          galleryUrls,
          isFeatured: form.isFeatured,
        }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to add product");
      }

      // Clear the product cache to ensure latest data is fetched
      try {
        await fetch("/api/products?featured=true", { 
          method: "GET",
          cache: "no-store",
          headers: { "x-invalidate-cache": "true" } 
        });
      } catch (cacheError) {
        console.error("Error clearing cache:", cacheError);
      }

      toast.success("Product added!");
      setForm({
        title: [""],
        shortDesc: [""],
        longDesc: [""],
        price: "",
        promotionalPricePercent: "",
        salesPricePercent: "",
        image: null,
        galleryImages: [],
        isFeatured: false, // <-- reset to default
      });
      setMainImagePreview(null);
      setGalleryPreviews([]);
      setSelectedCategory("");
      setSelectedSubcategory("");
      router.push("/products");
    } catch (error) {
      console.error("Error adding product:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add product.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-4 pb-12">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Package className="h-6 w-6" />
          <h1 className="text-2xl font-semibold">Add New Product</h1>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Save className="h-4 w-4" /> Save Draft
          </Button>
          <Button
            type="submit"
            form="product-form"
            className="flex items-center gap-2"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z" />
                </svg>
                Adding...
              </>
            ) : (
              <>
                <Check className="h-4 w-4" /> Add Product
              </>
            )}
          </Button>
        </div>
      </div>

      <form id="product-form" onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="pt-6">
            <h2 className="text-lg font-medium mb-4">General Information</h2>
            <div className="space-y-4">
              {/* Title array */}
              <div className="space-y-2">
                <Label>Name Product</Label>
                {form.title.map((val, idx) => (
                  <div key={idx} className="flex gap-2 mb-2">
                    <Input
                      name={`title-${idx}`}
                      placeholder={`Title ${idx + 1}`}
                      value={val}
                      onChange={e => handleArrayChange(e, "title", idx)}
                      required={idx === 0}
                      className="bg-gray-100"
                    />
                    {form.title.length > 1 && (
                      <Button type="button" variant="destructive" onClick={() => handleRemoveArrayField("title", idx)}>-</Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => handleAddArrayField("title")}>+ Add Title</Button>
              </div>
              {/* Short Desc array */}
              <div className="space-y-2">
                <Label>Short Description</Label>
                {form.shortDesc.map((val, idx) => (
                  <div key={idx} className="flex gap-2 mb-2">
                    <Input
                      name={`shortDesc-${idx}`}
                      placeholder={`Short Description ${idx + 1}`}
                      value={val}
                      onChange={e => handleArrayChange(e, "shortDesc", idx)}
                      required={idx === 0}
                      className="bg-gray-100"
                    />
                    {form.shortDesc.length > 1 && (
                      <Button type="button" variant="destructive" onClick={() => handleRemoveArrayField("shortDesc", idx)}>-</Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => handleAddArrayField("shortDesc")}>+ Add Short Desc</Button>
              </div>
              {/* Long Desc array */}
              <div className="space-y-2">
                <Label>Description Product</Label>
                {form.longDesc.map((val, idx) => (
                  <div key={idx} className="flex gap-2 mb-2">
                    <Textarea
                      name={`longDesc-${idx}`}
                      placeholder={`Long Description ${idx + 1}`}
                      value={val}
                      onChange={e => handleArrayChange(e, "longDesc", idx)}
                      required={idx === 0}
                      className="min-h-[80px] bg-gray-100"
                    />
                    {form.longDesc.length > 1 && (
                      <Button type="button" variant="destructive" onClick={() => handleRemoveArrayField("longDesc", idx)}>-</Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={() => handleAddArrayField("longDesc")}>+ Add Long Desc</Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-lg font-medium mb-4">Upload Product Image</h2>
            <div className="border rounded-lg p-4 bg-blue-50 flex flex-col items-center justify-center min-h-[260px] relative">
              {mainImagePreview ? (
                <>
                  <Image
                    src={mainImagePreview || "/placeholder.svg"}
                    alt="Product preview"
                    width={240}
                    height={240}
                    className="object-contain max-h-[240px]"
                  />
                  <label
                    htmlFor="image"
                    className="absolute bottom-4 right-4 bg-white rounded-full p-2 shadow cursor-pointer"
                  >
                    <Upload className="h-5 w-5" />
                    <input
                      id="image"
                      name="image"
                      type="file"
                      accept="image/*"
                      onChange={handleChange}
                      className="hidden"
                    />
                  </label>
                </>
              ) : (
                <label className="flex flex-col items-center justify-center gap-2 cursor-pointer w-full h-full">
                  <div className="bg-gray-100 rounded-full p-3">
                    <Upload className="h-6 w-6 text-gray-500" />
                  </div>
                  <span className="text-sm text-gray-500">Upload main product image</span>
                  <input
                    id="image"
                    name="image"
                    type="file"
                    accept="image/*"
                    onChange={handleChange}
                    className="hidden"
                    required
                  />
                </label>
              )}
            </div>
            <div className="flex gap-2 mt-4 overflow-x-auto pb-2">
              {galleryPreviews.map((preview, index) => (
                <div key={index} className="border rounded-md h-16 w-16 flex-shrink-0 overflow-hidden">
                  <Image
                    src={preview || "/placeholder.svg"}
                    alt={`Gallery image ${index + 1}`}
                    width={64}
                    height={64}
                    className="object-cover w-full h-full"
                  />
                </div>
              ))}
              <label className="border rounded-md h-16 w-16 flex-shrink-0 flex items-center justify-center cursor-pointer bg-blue-50">
                <Plus className="h-5 w-5 text-gray-400" />
                <input
                  name="galleryImages"
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleChange}
                  className="hidden"
                />
              </label>
            </div>
            {/* Move the featured checkbox here */}
            <div className="flex items-center gap-2 mt-4">
              <input
                type="checkbox"
                id="isFeatured"
                checked={form.isFeatured}
                onChange={handleCheckboxChange}
                className="h-4 w-4 accent-blue-600"
              />
              <Label htmlFor="isFeatured">Mark as Featured Product</Label>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-lg font-medium mb-4">Pricing</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">Base Pricing</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">$</span>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    placeholder="0.00"
                    value={form.price}
                    onChange={handleChange}
                    className="pl-8 bg-gray-100"
                    required
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="promotionalPricePercent">Promotional %</Label>
                <Input
                  id="promotionalPricePercent"
                  name="promotionalPricePercent"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={form.promotionalPricePercent}
                  onChange={handleChange}
                  className="bg-gray-100"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="salesPricePercent">Sales %</Label>
                <Input
                  id="salesPricePercent"
                  name="salesPricePercent"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={form.salesPricePercent}
                  onChange={handleChange}
                  className="bg-gray-100"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <h2 className="text-lg font-medium mb-4">Category</h2>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="category">Product Category</Label>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="subcategory">Product Subcategory</Label>
                <Select value={selectedSubcategory} onValueChange={setSelectedSubcategory}>
                  <SelectTrigger className="bg-green-100 border-gray-300 focus:ring-2 focus:ring-blue-200">
                    <SelectValue placeholder="Select Subcategory" />
                  </SelectTrigger>
                  <SelectContent>
                    {subcategories.map((subcategory) => (
                      <SelectItem key={subcategory.id} value={subcategory.id.toString()}>
                        {subcategory.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}

export default function AdminAddProductPage() {
  return (
    <AdminProtected>
      <AddProductPage />
    </AdminProtected>
  );
}