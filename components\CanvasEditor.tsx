import { useState, useRef } from 'react'
import { Stage, Layer, Text, Line } from 'react-konva'
import { KonvaEventObject } from 'konva/lib/Node'
import { Stage as StageType } from 'konva/lib/Stage'
import { Text as TextType } from 'konva/lib/shapes/Text'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AITextGenerator } from '@/components/ai-text-generator'

interface CanvasEditorProps {
  width?: number
  height?: number
  onSave?: (dataUrl: string) => void
  productType?: string
}

interface LineShape {
  points: number[]
}

interface TextShape {
  id: string
  x: number
  y: number
  text: string
  fontSize: number
  draggable: boolean
}

const CanvasEditor = ({ width = 800, height = 600, onSave, productType = 'product' }: CanvasEditorProps) => {
  const [tool, setTool] = useState<'pen' | 'text'>('pen')
  const [lines, setLines] = useState<LineShape[]>([])
  const [texts, setTexts] = useState<TextShape[]>([])
  const [isDrawing, setIsDrawing] = useState(false)
  const [isAIDialogOpen, setIsAIDialogOpen] = useState(false)
  const stageRef = useRef<StageType>(null)

  const handleMouseDown = (e: KonvaEventObject<MouseEvent>) => {
    if (tool === 'pen') {
      const stage = e.target.getStage()
      const pos = stage?.getPointerPosition()
      
      if (stage && pos) {
        setIsDrawing(true)
        setLines([...lines, { points: [pos.x, pos.y] }])
      }
    }
  }

  const handleMouseMove = (e: KonvaEventObject<MouseEvent>) => {
    if (!isDrawing || tool !== 'pen') return

    const stage = e.target.getStage()
    const point = stage?.getPointerPosition()
    
    if (stage && point && lines.length > 0) {
      const lastLine = lines[lines.length - 1]
      lastLine.points = lastLine.points.concat([point.x, point.y])
      setLines([...lines.slice(0, -1), lastLine])
    }
  }

  const handleMouseUp = () => {
    setIsDrawing(false)
  }

  const handleAddText = () => {
    const newText: TextShape = {
      id: Date.now().toString(),
      x: 50,
      y: 50,
      text: 'Double click to edit',
      fontSize: 20,
      draggable: true,
    }
    setTexts([...texts, newText])
  }

  const handleTextDblClick = (e: KonvaEventObject<MouseEvent>) => {
    const textNode = e.target as TextType
    if (textNode) {
      const currentText = textNode.text()
      const newText = prompt('Enter new text:', currentText)
      if (newText) {
        const updatedTexts = texts.map((t) => {
          if (t.id === textNode.id()) {
            return { ...t, text: newText }
          }
          return t
        })
        setTexts(updatedTexts)
      }
    }
  }

  const handleSave = () => {
    if (stageRef.current && onSave) {
      const dataUrl = stageRef.current.toDataURL()
      onSave(dataUrl)
    }
  }

  const handleAITextGenerated = (generatedText: string) => {
    const newText: TextShape = {
      id: Date.now().toString(),
      x: 50,
      y: 50,
      text: generatedText,
      fontSize: 20,
      draggable: true,
    }
    setTexts([...texts, newText])
    setIsAIDialogOpen(false)
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex gap-2 mb-4">
        <button
          className={`px-4 py-2 rounded ${tool === 'pen' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          onClick={() => setTool('pen')}
        >
          Pen
        </button>
        <button
          className={`px-4 py-2 rounded ${tool === 'text' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          onClick={() => setTool('text')}
        >
          Text
        </button>
        {tool === 'text' && (
          <>
            <button className="px-4 py-2 rounded bg-green-500 text-white" onClick={handleAddText}>
              Add Text
            </button>
            <Dialog open={isAIDialogOpen} onOpenChange={setIsAIDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="bg-purple-100 hover:bg-purple-200">
                  Generate with AI
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Generate Text with AI</DialogTitle>
                </DialogHeader>
                <AITextGenerator 
                  productType={productType}
                  onTextGenerated={handleAITextGenerated} 
                />
              </DialogContent>
            </Dialog>
          </>
        )}
        <button className="px-4 py-2 rounded bg-purple-500 text-white" onClick={handleSave}>
          Save Design
        </button>
      </div>

      <Stage
        width={width}
        height={height}
        onMouseDown={handleMouseDown}
        onMousemove={handleMouseMove}
        onMouseup={handleMouseUp}
        ref={stageRef}
        className="border border-gray-300 rounded-lg"
      >
        <Layer>
          {lines.map((line, i) => (
            <Line
              key={i}
              points={line.points}
              stroke="#000000"
              strokeWidth={5}
              tension={0.5}
              lineCap="round"
              lineJoin="round"
            />
          ))}
          {texts.map((text) => (
            <Text
              key={text.id}
              {...text}
              onDblClick={handleTextDblClick}
              onDragEnd={(e) => {
                const updatedTexts = texts.map((t) => {
                  if (t.id === text.id) {
                    return {
                      ...t,
                      x: e.target.x(),
                      y: e.target.y(),
                    }
                  }
                  return t
                })
                setTexts(updatedTexts)
              }}
            />
          ))}
        </Layer>
      </Stage>
    </div>
  )
}

export default CanvasEditor 