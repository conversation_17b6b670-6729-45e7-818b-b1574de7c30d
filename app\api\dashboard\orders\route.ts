import { auth } from "@/lib/auth";
import { NextResponse } from "next/server";
import { safeQuery } from "@/lib/db";
import NodeCache from "node-cache";

// Cache for dashboard orders (30 minutes TTL)
const ordersCache = new NodeCache({
  stdTTL: 1800, // 30 minutes
  checkperiod: 300, // Check for expired keys every 5 minutes
  useClones: false, // Don't clone objects for better performance
  maxKeys: 100 // Limit cache size
});

interface OrderItem {
  id: string;
  product_id: string;
  product_name: string;
  quantity: number;
  price_at_order: number;
  item_subtotal: number;
  
}

interface Order {
  order_id: string;
  order_number: string;
  total_amount: number;
  delivery_method: string;
  delivery_fee: number;
  promotional_discount: number;
  status: string;
  special_instructions: string | null;
  created_at: Date;
  updated_at: Date;
  street: string;
  city: string;
  province: string;
  postal_code: string;
  country: string;
  custom_design_image_url: string | null;
  order_items: OrderItem[];
}

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { user } = session;
    const isAdmin = user.userType === "admin";
    const userId = user.id;

    // Check cache first
    const cacheKey = `orders-${isAdmin ? 'admin' : userId}`;
    const cachedOrders = ordersCache.get(cacheKey);

    if (cachedOrders) {
      return NextResponse.json({
        success: true,
        data: cachedOrders,
      }, {
        headers: {
          'Cache-Control': 'max-age=1800, s-maxage=1800', // 30 minutes
        }
      });
    }

const baseQuery = `
  SELECT 
    o.id AS order_id,
    o.order_number,
    o.total_amount,
    o.delivery_method,
    o.delivery_fee,
    o.promotional_discount,
    o.status,
    o.special_instructions,
    o.custom_design_image_url,
    o.created_at,
    o.updated_at,
    da.street,
    da.city,
    da.province,
    da.postal_code,
    da.country,
    u.name AS customer_name,
    u.email AS customer_email,
    json_agg(
      json_build_object(
      'id', oi.id,
      'product_id', oi.product_id,
      'product_name', oi.product_name,
      'quantity', oi.quantity,
      'price_at_order', oi.price_at_order,
      'item_subtotal', oi.item_subtotal,
      'image_url', p.image_url,
      'title', p.title[1]  
    )
    ) AS order_items
  FROM orders o
  LEFT JOIN order_items oi ON oi.order_id = o.id
  LEFT JOIN delivery_addresses da ON da.order_id = o.id
  LEFT JOIN users u ON u.id = o.user_id
  LEFT JOIN product p ON p.id::text = oi.product_id
`;

const whereClause = isAdmin ? "" : "WHERE o.user_id = $1";

const groupBy = `
  GROUP BY o.id, da.id, u.id, u.name, u.email
  ORDER BY o.created_at DESC
`;

const query = `${baseQuery} ${whereClause} ${groupBy}`;

    console.log('Fetching orders for user:', userId, 'isAdmin:', isAdmin);

    const result = await safeQuery<Order>(
      query,
      isAdmin ? [] : [userId]
    );

    console.log('Orders fetched successfully, count:', result.rows.length);

    // Store in cache
    ordersCache.set(cacheKey, result.rows);

    // Add cache-control header for 30 minutes (1800 seconds)
    const response = NextResponse.json({
      success: true,
      data: result.rows,
    });

    response.headers.set('Cache-Control', 'max-age=1800, s-maxage=1800'); // 30 minutes

    return response;
  } catch (error) {
    console.error("[API /orders] Error:", error);

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
        console.error('Database connection limit reached in orders route');
        return NextResponse.json(
          { success: false, error: "Service temporarily unavailable. Please try again." },
          { status: 503 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Failed to fetch orders" },
      { status: 500 }
    );
  }
} 