'use client'

import { useState, useEffect, useRef } from 'react'
import dynamic from 'next/dynamic'
import { useWindowSize } from '@/hooks/useWindowSize'

const FlatdrawEditor = dynamic(() => import('./FlatdrawEditor'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-[400px] sm:h-[500px] md:h-[600px] border border-gray-300 rounded-lg flex items-center justify-center">
      Loading canvas editor...
    </div>
  ),
})

interface DesignDialogProps {
  isOpen: boolean
  onClose: () => void
  productId: string
  productName?: string
  productType?: string
  onDesignComplete: (designId: string) => void
}

const DesignDialog = ({ 
  isOpen, 
  onClose, 
  productId, 
  productName = '', 
  productType = '', 
  onDesignComplete 
}: DesignDialogProps) => {
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [productInfo, setProductInfo] = useState({
    name: productName,
    type: productType,
    imageUrl: ''
  })
  const containerRef = useRef<HTMLDivElement>(null)
  const { width } = useWindowSize()
  
  // Calculate canvas dimensions based on screen size
  const getCanvasDimensions = () => {
    if (width < 640) { // Mobile
      return { width: Math.min(width - 40, 400), height: 500 }
    } else if (width < 768) { // Small tablets
      return { width: Math.min(width - 80, 600), height: 500 }
    } else { // Larger screens
      return { width: 800, height: 600 }
    }
  }
  
  const canvasDimensions = getCanvasDimensions()

  // Fetch product information if not provided
  useEffect(() => {
    if (productId) {
      const fetchProductInfo = async () => {
        try {
          const response = await fetch(`/api/products/${productId}`)
          if (response.ok) {
            const data = await response.json()
            setProductInfo({
              name: data.title || productName,
              type: data.category_name || productType,
              imageUrl: data.image_url || ''
            })
          }
        } catch (error) {
          console.error('Error fetching product info:', error)
        }
      }
      
      fetchProductInfo()
    }
  }, [productId, productName, productType])

  if (!isOpen) return null

  const handleSaveDesign = async (dataUrl: string) => {
    try {
      setIsSaving(true)
      setError(null)

      // Generate a unique design ID
      const designId = `design_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      // Save to localStorage
      const designs = JSON.parse(localStorage.getItem('productDesigns') || '{}')
      designs[designId] = {
        id: designId,
        productId,
        designData: dataUrl,
        createdAt: new Date().toISOString()
      }
      localStorage.setItem('productDesigns', JSON.stringify(designs))

      // Call the completion handler with the design ID
      onDesignComplete(designId)
      onClose()
    } catch (error) {
      console.error('Error saving design:', error)
      setError('Failed to save your design. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-2 sm:p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[95vh] overflow-y-auto">
        <div className="p-3 sm:p-6" ref={containerRef}>
          <div className="flex justify-between items-center mb-2 sm:mb-4">
            <h2 className="text-xl sm:text-2xl font-bold">
              Customize Your Design
              {productInfo.name && (
                <span className="text-base sm:text-lg font-normal ml-2 text-gray-600">
                  for {productInfo.name}
                </span>
              )}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div className="mb-2 sm:mb-4">
            <p className="text-sm sm:text-base text-gray-600">
              Create your design using the tools below. You can draw, add text, shapes, and upload images. 
              When you&apos;re ready, click the Save button to continue to checkout.
            </p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-100 text-red-700 rounded">
              {error}
            </div>
          )}

          <div className="relative">
            <FlatdrawEditor
              initialWidth={canvasDimensions.width}
              initialHeight={canvasDimensions.height}
              backgroundColor="#ffffff"
              productImage={productInfo.imageUrl}
              onSave={handleSaveDesign}
              productId={productId}
              productName={productInfo.name}
              productType={productInfo.type}
            />
            {isSaving && (
              <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                <div className="text-lg">Saving your design...</div>
              </div>
            )}
          </div>

          <div className="flex justify-end mt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
              disabled={isSaving}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DesignDialog 