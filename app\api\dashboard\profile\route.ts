import { NextRequest, NextResponse } from "next/server";
import { safeQuery } from "@/lib/db"; // Use safe query instead of direct db
import { auth } from "@/lib/auth"; // Your NextAuth or custom auth logic
import NodeCache from "node-cache";

// Cache for user profiles (30 minutes TTL)
const profileCache = new NodeCache({
  stdTTL: 1800, // 30 minutes
  checkperiod: 300, // Check for expired keys every 5 minutes
  useClones: false, // Don't clone objects for better performance
  maxKeys: 200 // Limit cache size
});

export async function GET() {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, error: "Unauthorized" }, { status: 401 });
    }

    console.log('Fetching profile for user:', session.user.id);

    // Check cache first
    const cacheKey = `profile-${session.user.id}`;
    const cachedProfile = profileCache.get(cacheKey);

    if (cachedProfile) {
      return NextResponse.json({
        success: true,
        data: cachedProfile
      }, {
        headers: {
          'Cache-Control': 'max-age=1800, s-maxage=1800', // 30 minutes
        }
      });
    }

    const result = await safeQuery(
      `SELECT id, name, email, phone, street, city, province, postal_code, country
       FROM users WHERE id = $1`,
      [session.user.id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 });
    }

    console.log('Profile fetched successfully');

    // Store in cache
    profileCache.set(cacheKey, result.rows[0]);

    // Add cache-control header for 30 minutes (1800 seconds)
    const response = NextResponse.json({ success: true, data: result.rows[0] });
    response.headers.set('Cache-Control', 'max-age=1800, s-maxage=1800'); // 30 minutes

    return response;
  } catch (error) {
    console.error("GET /api/dashboard/profile error:", error);

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
        console.error('Database connection limit reached in profile route');
        return NextResponse.json({ success: false, error: "Service temporarily unavailable. Please try again." }, { status: 503 });
      }
    }

    return NextResponse.json({ success: false, error: "Failed to fetch profile" }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return NextResponse.json({ success: false, error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { name, email, phone, street, city, province, postal_code, country } = body;

    console.log('Updating profile for user:', session.user.id);

    const result = await safeQuery(
      `UPDATE users SET
        name = $1,
        email = $2,
        phone = $3,
        street = $4,
        city = $5,
        province = $6,
        postal_code = $7,
        country = $8,
        updated_at = NOW()
       WHERE id = $9
       RETURNING id, name, email, phone, street, city, province, postal_code, country`,
      [name, email, phone, street, city, province, postal_code, country, session.user.id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ success: false, error: "User not found" }, { status: 404 });
    }

    console.log('Profile updated successfully');

    return NextResponse.json({ success: true, data: result.rows[0] });
  } catch (error) {
    console.error("PUT /api/dashboard/profile error:", error);

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
        console.error('Database connection limit reached in profile update');
        return NextResponse.json({ success: false, error: "Service temporarily unavailable. Please try again." }, { status: 503 });
      }
    }

    return NextResponse.json({ success: false, error: "Failed to update profile" }, { status: 500 });
  }
}
