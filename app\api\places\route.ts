import { NextResponse } from 'next/server'

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('query')
  const placeId = searchParams.get('placeId')

  if (!query && !placeId) {
    return NextResponse.json({ error: 'Query or placeId is required' }, { status: 400 })
  }

  try {
    if (placeId) {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=address_components&key=${process.env.GOOGLE_MAPS_API_KEY}`
      )
      const data = await response.json()
      return NextResponse.json(data)
    } else {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${encodeURIComponent(query!)}&components=country:ca&types=address&key=${process.env.GOOGLE_MAPS_API_KEY}`
      )
      const data = await response.json()
      return NextResponse.json(data)
    }
  } catch (error) {
    console.error('Error fetching places:', error)
    return NextResponse.json({ error: 'Failed to fetch places' }, { status: 500 })
  }
} 