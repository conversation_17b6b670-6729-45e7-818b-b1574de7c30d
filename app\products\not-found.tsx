import Link from 'next/link';

export default function ProductNotFound() {
  return (
    <div className="container mx-auto px-4 py-16">
      <div className="flex flex-col items-center justify-center text-center">
        <h1 className="text-4xl font-bold text-blue-600 mb-4">Product Not Found</h1>
        <p className="text-gray-600 mb-8 max-w-lg">
          Sorry, we couldn&apos;t find the product you&apos;re looking for. It may have been removed or is temporarily unavailable.
        </p>
        <div className="flex gap-4">
          <Link 
            href="/products" 
            prefetch={true}
            className="px-6 py-3 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors"
          >
            Browse All Products
          </Link>
          <Link 
            href="/" 
            prefetch={true}
            className="px-6 py-3 border border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-50 transition-colors"
          >
            Return to Home
          </Link>
        </div>
      </div>
    </div>
  );
} 