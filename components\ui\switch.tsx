"use client";
import * as React from "react";

type SwitchProps = React.HTMLAttributes<HTMLSpanElement>;

export const Switch = React.forwardRef<HTMLInputElement, SwitchProps>(
  ({ className, ...props }, ref) => (
    <input
      type="checkbox"
      ref={ref}
      className={`h-5 w-10 rounded-full border transition-colors ${className || ""}`}
      {...props}
    />
  )
);
Switch.displayName = "Switch";