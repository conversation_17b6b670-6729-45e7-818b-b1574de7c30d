import fs from 'fs';
import path from 'path';
import { Product } from '../../types';
import ProductDetail from './productDetail';

// Get product by ID for server component
async function getProduct(id: string): Promise<Product | null> {
  const filePath = path.join(process.cwd(), 'data', 'products.json');
  let product: Product | null = null;

  try {
    const fileData = fs.readFileSync(filePath, 'utf8');
    const products: Product[] = JSON.parse(fileData);
    const foundProduct = products.find(item => item.id === Number(id));
    if (foundProduct) {
      product = foundProduct;
    }
  } catch (error) {
    console.error('Error reading product data:', error);
  }

  return product;
}

interface Props {
  params: Promise<{ id: string }>;
}

export async function generateMetadata({ params }: Props) {
  const resolvedParams = await params;
  const product = await getProduct(resolvedParams.id);
  
  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }
  
  const title = typeof product.title === 'string' ? product.title : product.title[0];
  const description = typeof product.shortDesc === 'string' ? product.shortDesc : product.shortDesc[0];
  
  return {
    title,
    description,
  };
}

export default async function ProductPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const product = await getProduct(resolvedParams.id);
  
  if (!product) {
    return (
      <div className="container mx-auto py-20 text-center text-gray-500 text-xl">
        Product not found
      </div>
    );
  }
  
  return <ProductDetail product={product} />;
}