import { NextResponse } from "next/server";
import { revalidateTag } from "next/cache";
import { auth } from "@/lib/auth";

// This endpoint allows manual revalidation of dashboard data
// It can be called when data changes that should be reflected immediately
export async function POST() {
  try {
    const session = await auth();

    // Only admins can trigger revalidation
    if (!session?.user?.id || session.user.userType !== "admin") {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Revalidate the dashboard data
    revalidateTag("dashboard-data");

    return NextResponse.json({
      success: true,
      message: "Dashboard data revalidated",
    });
  } catch (error) {
    console.error("Revalidation error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to revalidate data" },
      { status: 500 }
    );
  }
} 