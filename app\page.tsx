import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Award, Clock, Printer, Truck } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import FeaturedProductsSlider from "@/components/FeaturedProductsSlider";

type Product = {
  id: string | number;
  title: string | string[];
  short_description: string | string[];
  long_description: string | string[];
  price: number | string;
  promotional_price_percent?: number | string;
  sales_price_percent?: number | string;
  image_url: string;
  express?: boolean;
};

async function getFeaturedProducts(): Promise<Product[]> {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_BASE_URL}/api/products?featured=true`,
      {
        next: { revalidate: 3600 },
      }
    );

    if (!res.ok) {
      console.error(
        "Failed to fetch featured products:",
        res.status,
        await res.text()
      );
      return [];
    }
    const data = await res.json();
    return data.products || [];
  } catch (error) {
    console.error("Error fetching featured products:", error);
    return [];
  }
}

export default async function Home() {
  const featuredProducts = await getFeaturedProducts();

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="bg-blue-600 text-white">
        <div className="container mx-auto px-8 py-12 md:py-24 flex flex-col md:flex-row items-center">
          <div className="md:w-1/2 mb-8 md:mb-0">
            <h1
              className="text-4xl md:text-5xl font-bold mb-4 animate-slide-in-bottom"
              style={{ animationDelay: "0.1s" }}
            >
              Professional Printing Services for Your Business
            </h1>
            <p
              className="text-xl mb-6 animate-slide-in-bottom"
              style={{ animationDelay: "0.2s" }}
            >
              High-quality printing solutions with fast turnaround times and
              competitive pricing.
            </p>
            <div
              className="flex flex-col sm:flex-row gap-4 animate-slide-in-bottom"
              style={{ animationDelay: "0.3s" }}
            >
              <Link prefetch={true} href="/products">
                <Button
                  size="lg"
                  className="bg-white text-blue-600 hover:bg-gray-100"
                >
                  Shop Products
                </Button>
              </Link>
            </div>
          </div>
          <div className="md:w-1/2 flex justify-center animate-slide-in-bottom">
            <Image
              src="/images/hero.jpeg"
              alt="Printing Services"
              width={600}
              height={400}
              className="rounded-lg shadow-lg"
            />
          </div>
        </div>
      </section>

      {/* Featured Products Carousel - Now using the client component */}
      <FeaturedProductsSlider initialFeaturedProducts={featuredProducts} />

      {/* Services */}
      <section className="px-8 py-12">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Our Printing Services
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="printcloud-card-hover">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Clock className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Express Printing</h3>
                <p className="text-gray-600 mb-4">
                  Get your printing done in as little as 24 hours with our
                  express printing service.
                </p>
                <Link
                  href="/express-printing"
                  prefetch={true}
                  className="text-blue-600 hover:text-blue-800 mt-auto flex items-center"
                >
                  Learn More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </CardContent>
            </Card>

            <Card className="printcloud-card-hover">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Printer className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Custom Printing</h3>
                <p className="text-gray-600 mb-4">
                  Personalized printing solutions tailored to your specific
                  business needs.
                </p>
                <Link
                  prefetch={true}
                  href="/custom-printing"
                  className="text-blue-600 hover:text-blue-800 mt-auto flex items-center"
                >
                  Learn More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </CardContent>
            </Card>

            <Card className="printcloud-card-hover">
              <CardContent className="p-6 flex flex-col items-center text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Truck className="h-8 w-8 text-blue-600" />
                </div>
                <h3 className="text-xl font-bold mb-2">Print Fulfillment</h3>
                <p className="text-gray-600 mb-4">
                  End-to-end print fulfillment services including storage,
                  packaging, and shipping.
                </p>
                <Link
                  prefetch={true}
                  href="/print-fulfillment"
                  className="text-blue-600 hover:text-blue-800 mt-auto flex items-center"
                >
                  Learn More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="px-8 py-12 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">
            Why Choose PrintCloud
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">Quality Guaranteed</h3>
              <p className="text-gray-300">
                We use premium materials and state-of-the-art printing
                technology to ensure the highest quality.
              </p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <Clock className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">Fast Turnaround</h3>
              <p className="text-gray-300">
                With our express printing options, get your orders completed in
                as little as 24-48 hours.
              </p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <Truck className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold mb-2">Free Shipping</h3>
              <p className="text-gray-300">
                Enjoy free shipping on all orders over $149 across Canada.
              </p>
            </div>

            <div className="flex flex-col items-center text-center">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="h-8 w-8 text-white"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-2">Competitive Pricing</h3>
              <p className="text-gray-300">
                Get the best value for your money with our competitive pricing
                and bulk discounts.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="px-8 py-12 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Whether you need business cards, flyers, posters, or custom printing
            solutions, we&apos;re here to help.ve got you covered.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link prefetch={true} href="/products">
              <Button
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100"
              >
                Shop Now
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
