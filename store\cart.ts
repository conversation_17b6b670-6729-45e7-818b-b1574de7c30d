import { create } from "zustand"
import { persist } from "zustand/middleware"

export interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
  image: string
  isPromotional?: boolean
}

interface CartStore {
  items: CartItem[]
  deliveryMethod: "PICKUP" | "DELIVERY"
  addItem: (item: CartItem) => void
  removeItem: (id: string) => void
  updateQuantity: (id: string, quantity: number) => void
  clearCart: () => void
  setDeliveryMethod: (method: "PICKUP" | "DELIVERY") => void
  getSubtotalPrice: () => number
  hasPromotionalItems: () => boolean
  getPromotionalDiscount: () => number
  getItemsCount: () => number
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      deliveryMethod: "PICKUP",
      addItem: (item) => {
        set((state) => {
          const existingItem = state.items.find((i) => i.id === item.id)
          if (existingItem) {
            return {
              items: state.items.map((i) =>
                i.id === item.id
                  ? { ...i, quantity: i.quantity + item.quantity }
                  : i
              ),
            }
          }
          return { items: [...state.items, item] }
        })
      },
      removeItem: (id) => {
        set((state) => ({
          items: state.items.filter((item) => item.id !== id),
        }))
      },
      updateQuantity: (id, quantity) => {
        set((state) => ({
          items: state.items.map((item) =>
            item.id === id ? { ...item, quantity } : item
          ),
        }))
      },
      clearCart: () => {
        set({ items: [] })
      },
      setDeliveryMethod: (method) => {
        set({ deliveryMethod: method })
      },
      getSubtotalPrice: () => {
        return get().items.reduce(
          (total, item) => total + item.price * item.quantity,
          0
        )
      },
      hasPromotionalItems: () => {
        return get().items.some((item) => item.isPromotional)
      },
      getPromotionalDiscount: () => {
        const subtotal = get().getSubtotalPrice()
        return get().hasPromotionalItems() ? subtotal * 0.1 : 0
      },
      getItemsCount: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0)
      }
    }),
    {
      name: "cart-storage",
    }
  )
) 