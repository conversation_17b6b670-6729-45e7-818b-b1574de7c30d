import { Pool, QueryResultRow } from 'pg'; // Added QueryResultRow type
// Assume getPool is imported from your db.ts file
import { getPool } from './db'; // Adjust path as necessary

// Track active database requests for monitoring
let activeRequests = 0;
export function getActiveRequests(): number {
  return activeRequests;
}

function incrementActiveRequests() {
  activeRequests++;
  // console.log(`Active DB requests: ${activeRequests}`);
}

function decrementActiveRequests() {
  activeRequests--;
  // console.log(`Active DB requests: ${activeRequests}`);
}

// Mock for db-circuit-breaker.ts for illustrative purposes
// In a real scenario, this would be a proper circuit breaker implementation
// e.g., using a library like 'opossum'
async function executeWithCircuitBreaker<T>(fn: () => Promise<T>): Promise<T> {
  // Simplified: real circuit breaker would have state (CLOSED, OPEN, HALF_OPEN)
  // and logic to trip/reset.
  // console.log('CircuitBreaker: Attempting operation.');
  try {
    const result = await fn();
    // console.log('CircuitBreaker: Operation successful.');
    return result;
  } catch (error) {
    // console.error('CircuitBreaker: Operation failed.', error);
    // In a real breaker, this error might cause it to trip.
    throw error; // Re-throw the error
  }
}
// End of mock

// Type-safe query function with better connection management
export async function safeQuery<T extends QueryResultRow = QueryResultRow>(
  text: string,
  params?: unknown[]
): Promise<{ rows: T[]; rowCount: number }> {
  // Dynamic import: Loads the circuit breaker logic only when safeQuery is first called.
  // This can be good for initial startup time but adds a slight overhead to the first call.
  // For frequently called functions, a static import at the top might be preferred.
  // const { executeWithCircuitBreaker } = await import('./db-circuit-breaker');

  return executeWithCircuitBreaker(async () => {
    incrementActiveRequests();

    try {
      const pool: Pool = getPool(); // Ensure getPool() is available and typed
      
      // Using the generic type T with pool.query for better type safety on result.rows
      // T here represents the type of a single row object.
      const result = await pool.query<T>(text, params);
      
      return {
        rows: result.rows, // result.rows will be T[]
        rowCount: result.rowCount === null ? (result.command === 'SELECT' ? result.rows.length : 0) : result.rowCount
        // result.rowCount can be null for some statements.
        // For SELECT, if rowCount is null (should be rare), rows.length is a fallback.
        // For non-SELECT (INSERT, UPDATE, DELETE), if rowCount is null, it implies 0 affected rows.
      };
    } finally {
      decrementActiveRequests();
    }
  });
} 