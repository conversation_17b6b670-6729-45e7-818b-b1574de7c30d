import Link from "next/link";

export const metadata = {
  title: "About Us | PrintCloud",
  description: "Learn about PrintCloud, your reliable printing partner",
};

export default function About() {
  return (
    <div className="container mx-auto px-8 py-12">
      <h1 className="text-3xl font-bold mb-6">About PrintCloud</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        <div>
          <h2 className="text-2xl font-semibold mb-4">Our Story</h2>
          <p className="text-gray-700 mb-4">
            Founded in 2018, PrintCloud started with a simple mission: to provide high-quality printing solutions that help businesses stand out. What began as a small printing shop has grown into a full-service print provider serving clients across Canada.
          </p>
          <p className="text-gray-700 mb-4">
            Our journey has been defined by our commitment to quality, innovation, and exceptional customer service. We&apos;ve invested in state-of-the-art printing technology and a talented team that understands the importance of delivering projects on time and exceeding expectations.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Our Mission</h2>
          <p className="text-gray-700 mb-4">
            To provide businesses with high-quality, affordable, and reliable printing services that help them communicate effectively with their audience and achieve their marketing goals.
          </p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Our Vision</h2>
          <p className="text-gray-700 mb-4">
            To be the most trusted printing partner for businesses of all sizes, known for our quality, reliability, and innovative solutions that help our clients succeed.
          </p>
        </div>
        
        <div>
          <div className="bg-gray-100 p-8 rounded-lg mb-8">
            <h2 className="text-2xl font-semibold mb-4">Why Choose PrintCloud</h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <div className="bg-blue-600 rounded-full p-1 mt-1 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Quality Guaranteed</h3>
                  <p className="text-gray-700">We use premium materials and advanced printing technology to ensure exceptional quality for every project.</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-blue-600 rounded-full p-1 mt-1 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Fast Turnaround</h3>
                  <p className="text-gray-700">Our efficient processes and state-of-the-art equipment allow us to offer industry-leading turnaround times.</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-blue-600 rounded-full p-1 mt-1 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Expert Support</h3>
                  <p className="text-gray-700">Our team of printing specialists is ready to help with any questions and ensure your project turns out perfectly.</p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-blue-600 rounded-full p-1 mt-1 mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold">Competitive Pricing</h3>
                  <p className="text-gray-700">We offer transparent pricing and volume discounts to ensure you get the best value for your investment.</p>
                </div>
              </li>
            </ul>
          </div>
          
          <h2 className="text-2xl font-semibold mb-4">Our Process</h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">1</div>
              <div>
                <h3 className="font-semibold">Plan</h3>
                <p className="text-gray-700">We work with you to understand your needs and requirements.</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">2</div>
              <div>
                <h3 className="font-semibold">Design</h3>
                <p className="text-gray-700">Our design team can create or optimize your artwork for printing.</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">3</div>
              <div>
                <h3 className="font-semibold">Print</h3>
                <p className="text-gray-700">We use advanced technology to produce high-quality prints.</p>
              </div>
            </div>
            <div className="flex items-center">
              <div className="bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-3">4</div>
              <div>
                <h3 className="font-semibold">Deliver</h3>
                <p className="text-gray-700">We ensure your prints are delivered safely and on time.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-12 bg-blue-600 text-white p-8 rounded-lg text-center">
        <h2 className="text-2xl font-semibold mb-4">Ready to Get Started?</h2>
        <p className="mb-6">Connect with our team to discuss your printing needs and get a custom quote.</p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/contact"
            prefetch={true}
            className="bg-white text-blue-600 hover:bg-gray-100 transition-colors px-6 py-3 rounded-md font-medium"
          >
            Contact Us
          </Link>
          <Link
            href="/products"
            prefetch={true}
            className="bg-transparent hover:bg-blue-700 border border-white transition-colors px-6 py-3 rounded-md font-medium"
          >
            View Products
          </Link>
        </div>
      </div>
    </div>
  );
} 