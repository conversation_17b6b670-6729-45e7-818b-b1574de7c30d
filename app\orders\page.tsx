"use client"

import { useEffect, useState, Suspense } from "react"
import { useAuth } from "@/hooks/useAuth"
import { But<PERSON> } from "@/components/ui/button"
import { Clock, MapPin, Truck } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { OrdersPageSkeleton } from "@/components/OrdersPageSkeleton"

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image: string
}

interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
}

interface Order {
  id: string
  orderNumber: string
  status: string
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
  promotionalDiscount: number
  totalAmount: number
  deliveryMethod: "PICKUP" | "DELIVERY"
  deliveryAddress?: DeliveryAddress
  specialInstructions?: string
  createdAt: string
  estimatedDeliveryTime?: string
  customDesignImageUrl?: string | null
}

export default function OrdersPage() {
  return (
    <Suspense fallback={<OrdersPageSkeleton />}>
      <OrdersContent />
    </Suspense>
  )
}

function OrdersContent() {
  const { isAuthenticated } = useAuth()
  const [orders, setOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const response = await fetch("/api/orders")
        if (!response.ok) {
          throw new Error("Failed to fetch orders")
        }
        const data = await response.json()
        setOrders(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load orders")
      } finally {
        setIsLoading(false)
      }
    }

    if (isAuthenticated) {
      fetchOrders()
    }
  }, [isAuthenticated])

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8 mt-[120px] text-center">
        <h1 className="text-2xl font-bold mb-4">Please sign in to view your orders</h1>
        <Link href="/login" prefetch={true}>
          <Button>Sign In</Button>
        </Link>
      </div>
    )
  }

  if (isLoading) {
    return <OrdersPageSkeleton />
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 mt-[120px] text-center">
        <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
        <p className="mb-4">{error}</p>
        <Link href="/" prefetch={true}>
          <Button>Return to Home</Button>
        </Link>
      </div>
    )
  }

  if (orders.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8 mt-[120px] text-center">
        <h1 className="text-2xl font-bold mb-4">No Orders Yet</h1>
        <p className="text-gray-600 mb-8">You haven&apos;t placed any orders yet.</p>
        <Link href="/products" prefetch={true}>
          <Button>Start Shopping</Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 mt-[120px]">
      <h1 className="text-2xl md:text-3xl font-bold mb-8">My Orders</h1>
      <div className="space-y-6">
        {orders.map((order) => (
          <div key={order.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="p-6">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold">Order #{order.orderNumber}</h2>
                  <p className="text-gray-600">
                    Placed on {new Date(order.createdAt).toLocaleString()}
                  </p>
                </div>
                <div className="mt-4 md:mt-0">
                  <Link href={`/order-success?orderId=${order.id}`} prefetch={true}>
                    <Button variant="outline">View Details</Button>
                  </Link>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="flex items-start">
                  <Clock className="w-5 h-5 mr-3 text-gray-600 mt-1" />
                  <div>
                    <h3 className="font-medium">Status</h3>
                    <p className="text-gray-600 capitalize">{order.status.toLowerCase()}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  {order.deliveryMethod === "DELIVERY" ? (
                    <>
                      <Truck className="w-5 h-5 mr-3 text-gray-600 mt-1" />
                      <div>
                        <h3 className="font-medium">Delivery</h3>
                        <p className="text-gray-600">
                          {order.estimatedDeliveryTime
                            ? `Estimated delivery: ${order.estimatedDeliveryTime}`
                            : "In transit"}
                        </p>
                      </div>
                    </>
                  ) : (
                    <>
                      <MapPin className="w-5 h-5 mr-3 text-gray-600 mt-1" />
                      <div>
                        <h3 className="font-medium">Pickup</h3>
                        <p className="text-gray-600">Ready for pickup</p>
                      </div>
                    </>
                  )}
                </div>
              </div>

              <div className="border-t pt-6">
                <div className="space-y-4">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex flex-col gap-4">
                      <div className="flex items-center gap-4">
                        <div className="relative w-16 h-16">
                          <Image
                            src={item.image}
                            alt={item.name}
                            fill
                            className="object-cover rounded-lg"
                          />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{item.name}</h3>
                          <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                          <p className="text-blue-600 font-medium">
                            ${(Number(item.price) * item.quantity).toFixed(2)}
                          </p>
                        </div>
                      </div>
                      
                      {order.customDesignImageUrl && (
                        <div className="mt-2 pl-20">
                          <p className="text-sm font-medium text-gray-700 mb-1">Custom Design Included</p>
                          <div className="relative w-full h-[100px] border border-gray-200 rounded-lg overflow-hidden">
                            <Image
                              src={order.customDesignImageUrl}
                              alt="Custom Design"
                              fill
                              className="object-contain"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="mt-6 pt-6 border-t">
                  <div className="flex justify-between text-gray-600">
                    <span>Subtotal</span>
                    <span>${Number(order.subtotal).toFixed(2)}</span>
                  </div>

                  {order.deliveryMethod === "DELIVERY" && (
                    <div className="flex justify-between text-gray-600">
                      <span>Delivery Fee</span>
                      <span>${Number(order.deliveryFee).toFixed(2)}</span>
                    </div>
                  )}

                  {order.promotionalDiscount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Promotional Discount</span>
                      <span>-${Number(order.promotionalDiscount).toFixed(2)}</span>
                    </div>
                  )}

                  <div className="flex justify-between font-bold text-lg pt-2 border-t mt-2">
                    <span>Total</span>
                    <span className="text-blue-600">
                      ${Number(order.totalAmount).toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
} 