import { Suspense } from 'react';
import ProductsClient from './products-client';

// Define the same Product type as in products-client.tsx
interface Product {
  id: string;
  title: string[];
  short_description: string[];
  long_description: string[];
  price: number;
  promotional_price_percent?: number;
  sales_price_percent?: number;
  image_url: string;
  gallery_urls?: string[];
  created_at?: string;
  category_id?: string;
  category_name?: string;
}

// Define Category type
interface Category {
  id: string;
  name: string;
}

async function getInitialProductsAndCategories(category?: string, sort?: string): Promise<{ products: Product[], categories: Category[] }> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    
    // Fetch categories
    const catRes = await fetch(`${baseUrl}/api/categories`, { next: { revalidate: 3600 } }); // Cache categories for 1 hour
    const catData = catRes.ok ? await catRes.json() : { categories: [] };
    const categories = catData.categories || [];

    // Fetch initial products
    let productUrl = `${baseUrl}/api/products`;
    const params = new URLSearchParams();
    if (category) {
      params.append("category", category);
    }
    if (sort) {
      params.append("sort", sort);
    }
    if (params.toString()) {
      productUrl += `?${params.toString()}`;
    }

    // Use cache for products to prevent repeated loading
    const prodRes = await fetch(productUrl, { 
      next: { revalidate: 300 }, // Cache for 5 minutes
      cache: 'force-cache' 
    });
    const prodData = prodRes.ok ? await prodRes.json() : { products: [] };
    const products = prodData.products || [];

    return { products, categories };
  } catch (error) {
    console.error("Error fetching initial products/categories:", error);
    return { products: [], categories: [] };
  }
}

export default async function ProductsPage({ 
  searchParams 
}: { 
  searchParams: Promise<{ category?: string; sort?: string }> 
}) {
  // Await the searchParams Promise to access its properties
  const params = await searchParams;
  const initialCategory = params.category;
  const initialSort = params.sort;
  
  // Fetch data with appropriate caching strategy
  const { products: initialProducts, categories: initialCategories } = await getInitialProductsAndCategories(initialCategory, initialSort);

  return (
    <Suspense fallback={<ProductsLoading />}>
      <ProductsClient 
        initialProducts={initialProducts} 
        initialCategories={initialCategories} 
      />
    </Suspense>
  );
}

function ProductsLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Our Products</h1>
        <div className="flex gap-4">
          <div className="h-10 w-24 bg-gray-200 animate-pulse rounded-md"></div>
          <div className="h-10 w-24 bg-gray-200 animate-pulse rounded-md"></div>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 xl:gap-6">
        {Array.from({ length: 8 }).map((_, i) => (
          <div key={i} className="bg-gray-100 animate-pulse h-96 rounded-lg"></div>
        ))}
      </div>
    </div>
  );
}