"use client";

import { useState, useMemo } from "react";
import Image from "next/image";
import Link from "next/link";
import { ArrowLeft, ShoppingCart, Star } from "lucide-react";
import DesignDialog from "@/components/DesignDialog";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

interface Product {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  created_at?: string;
}

interface ProductDetailProps {
  product: Product;
}

console.log("classic");

const ProductGalleryThumbnails = ({
  product,
  selectedImage,
  setSelectedImage,
}: ProductDetailProps & {
  selectedImage: string;
  setSelectedImage: (img: string) => void;
}) => (
  <>
    {product.gallery_urls && product.gallery_urls.length > 0 && (
      <div className="grid grid-cols-4 gap-3 mt-6">
        {[product.image_url, ...product.gallery_urls].map((img) => (
          <button
            key={img}
            onClick={() => setSelectedImage(img)}
            className={`relative aspect-square overflow-hidden rounded-lg ${
              selectedImage === img ? "ring-2 ring-primary ring-offset-2" : ""
            }`}
          >
            <Image
              src={img}
              alt={`${product.title} gallery image`}
              fill
              className="object-cover"
            />
          </button>
        ))}
      </div>
    )}
  </>
);

const extractHighlights = (longDescription: string | string[]): string[] => {
  if (!longDescription) return [];
  const descriptionText = Array.isArray(longDescription)
    ? longDescription.join("\n")
    : longDescription;
  if (descriptionText.includes("|")) {
    return descriptionText
      .split("|")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  if (descriptionText.includes("\n")) {
    return descriptionText
      .split("\n")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  if (
    descriptionText.includes("High Quality Print") &&
    descriptionText.includes("Customization Available")
  ) {
    return [
      "High Quality Print",
      "Customization Available",
      "Fast Processing",
      "Premium Materials",
    ];
  }
  return descriptionText
    .split(/[.!?]/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

const CheckoutSection = ({ product }: ProductDetailProps) => {
  const [isDesignDialogOpen, setIsDesignDialogOpen] = useState(false);
  const router = useRouter();

  const handleContinueToCheckout = () => {
    setIsDesignDialogOpen(true);
  };

  const handleDesignComplete = (designId: string) => {
    localStorage.setItem(
      "selectedProduct",
      JSON.stringify({
        id: product.id,
        title: product.title,
        price: product.price,
        image_url: product.image_url,
      })
    );
    router.push(`/checkout?product=${product.id}&designId=${designId}`);
  };

  return (
    <>
      <div className="flex flex-col gap-3 mt-6">
        <Button
          onClick={handleContinueToCheckout}
          size="lg"
          className="w-full bg-blue-600 hover:bg-blue-700"
        >
          <ShoppingCart className="mr-2 h-5 w-5" />
          Continue to Checkout
        </Button>
      </div>
      <DesignDialog
        isOpen={isDesignDialogOpen}
        onClose={() => setIsDesignDialogOpen(false)}
        productId={product.id.toString()}
        productName={product.title}
        onDesignComplete={handleDesignComplete}
      />
    </>
  );
};

const ProductDetailClassic = ({ product }: ProductDetailProps) => {
  const [selectedImage, setSelectedImage] = useState(product.image_url);

  const formattedTime = product.created_at
    ? new Date(product.created_at).toLocaleDateString("en-US", {
        month: "2-digit",
        day: "2-digit",
        year: "numeric",
      })
    : "NEW ARRIVAL";

  const productHighlights = useMemo(
    () => extractHighlights(product.long_description),
    [product.long_description]
  );

  const formattedDescription = useMemo(() => {
    const descriptionText = Array.isArray(product.long_description)
      ? product.long_description.join("\n")
      : product.long_description;
    if (
      productHighlights.every(
        (highlight) => !descriptionText.includes(highlight)
      )
    ) {
      return descriptionText;
    }
    return descriptionText;
  }, [product.long_description, productHighlights]);

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section with Overlapping Elements */}
      <div className="relative">
        {/* Main Product Image */}
        <div className="relative h-[70vh] w-full overflow-hidden">
          <Image
            src={selectedImage}
            alt={product.title}
            className="object-cover"
            fill
            priority
          />
        </div>

        {/* Overlapping Content Card */}
        <div className="absolute bottom-0 right-0 bg-white p-6 md:p-8 md:w-3/4 lg:w-2/3 xl:w-1/2 transform translate-y-1/4 shadow-lg">
          <div className="inline-block bg-red-600 text-white px-4 py-1 text-sm font-bold mb-4">
            {formattedTime}
          </div>
          <Link
            href="/products"
            prefetch={true}
            className="flex items-center gap-1.5 px-3 py-1.5 text-sm bg-white text-blue-600 hover:bg-blue-50 border border-blue-600 rounded-md transition-colors duration-200 mb-4 w-fit"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to products</span>
          </Link>
          <h1 className="text-3xl md:text-4xl font-bold mb-2">
            {product.title}
          </h1>
          <p className="text-lg text-muted-foreground mb-4">
            {product.short_description}
          </p>
          <div className="flex items-center text-sm text-muted-foreground">
            <span className="font-bold text-foreground">By</span>
            <span className="uppercase ml-2 font-bold">PRINTCLOUD DESIGN</span>
          </div>
        </div>
      </div>

      {/* Main Content with Overlapping Sections */}
      <div className="container mx-auto mt-32 px-4 md:px-6 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Main Content - Now on the left */}
          <div className="lg:col-span-9 order-1">
            {/* Product Gallery Thumbnails */}
            <ProductGalleryThumbnails
              product={product}
              selectedImage={selectedImage}
              setSelectedImage={setSelectedImage}
            />

            {/* Product Description with Overlapping Sections */}
            <div className="relative mt-12">
              {/* First Section */}
              <div className="mb-12">
                <h2 className="text-2xl font-bold mb-4">Product Description</h2>
                <p className="text-muted-foreground whitespace-pre-line">
                  {formattedDescription}
                </p>
              </div>

              {/* Overlapping Image Section */}
              {product.gallery_urls && product.gallery_urls.length > 0 && (
                <div className="relative mb-16">
                  <div className="relative h-[400px] w-full overflow-hidden rounded-lg">
                    <Image
                      src={product.gallery_urls[0]}
                      alt={`${product.title} feature highlight`}
                      className="object-cover"
                      fill
                    />
                  </div>
                  {/* Overlapping Content Box */}
                  <div className="absolute -bottom-8 right-0 bg-white p-6 shadow-lg max-w-md">
                    <div className="bg-blue-600 text-white px-2 py-0.5 text-xs inline-block mb-2">
                      FEATURED
                    </div>
                    <h3 className="text-xl font-bold mb-2">Premium Quality</h3>
                    <p className="text-sm text-muted-foreground">
                      Our products are made with the highest quality materials
                      to ensure durability and customer satisfaction.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="lg:col-span-3 order-2">
            <div className="sticky top-8 space-y-8">
              {productHighlights.length > 0 && (
                <div>
                  <h3 className="font-bold mb-4">Product Highlights</h3>
                  <ul className="space-y-2 text-sm">
                    {productHighlights.map((highlight, index) => (
                      <li key={index} className="flex items-start">
                        <span className="bg-primary/10 text-primary rounded-full p-1 mr-2 flex-shrink-0">
                          <Star className="h-3 w-3" />
                        </span>
                        <span>{highlight}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              <div>
                <h3 className="font-bold mb-4">Price</h3>
                <div className="text-3xl font-bold text-blue-600">
                  $
                  {typeof product.price === "string"
                    ? parseFloat(product.price).toFixed(2)
                    : product.price.toFixed(2)}{" "}
                  CAD
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  Free shipping on orders over $50
                </div>
              </div>

              {/* Checkout Actions */}
              <CheckoutSection product={product} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailClassic;
