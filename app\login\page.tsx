"use client";

import { useState, Suspense } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { signIn } from "next-auth/react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { FaApple, FaGoogle } from "react-icons/fa";
import { Separator } from "@/components/ui/separator";

const loginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z
    .string()
    .min(6, { message: "Password must be at least 6 characters" }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [socialLoading, setSocialLoading] = useState<string | null>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const returnUrl = searchParams.get("returnUrl") || "/";

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(data: LoginFormValues) {
    setIsLoading(true);

    try {
      const result = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: false,
      });

      if (result?.error) {
        toast("Something went wrong. Please try again.", {
          description: "Failed to sign in. Please check your credentials.",
        });
      } else {
        toast("Sign in successful", {
          description: "You have been signed in successfully.",
        });
        router.push(returnUrl);
        router.refresh();
      }
    } catch (error) {
      toast("Error signing in", {
        description:
          "An error occurred while signing in. Please try again later.",
      });
      console.log(error);
    } finally {
      setIsLoading(false);
    }
  }

  const handleSocialSignIn = async (provider: "google" | "apple") => {
    try {
      setSocialLoading(provider);
      await signIn(provider, { 
        callbackUrl: returnUrl,
        redirect: true
      });
    } catch (error) {
      toast("Error signing in", {
        description: `Failed to sign in with ${provider}. Please try again.`,
      });
      console.error(error);
      setSocialLoading(null);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="flex justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-2xl font-bold">Sign In</CardTitle>
            <CardDescription>
              Enter your email and password to access your account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col space-y-3">
              <Button 
                type="button" 
                variant="outline" 
                className="w-full flex items-center justify-center gap-2"
                onClick={() => handleSocialSignIn("google")}
                disabled={socialLoading !== null}
              >
                {socialLoading === "google" ? (
                  <span>Signing in...</span>
                ) : (
                  <>
                    <FaGoogle className="h-4 w-4" />
                    <span>Sign in with Google</span>
                  </>
                )}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                className="w-full flex items-center justify-center gap-2"
                onClick={() => handleSocialSignIn("apple")}
                disabled={socialLoading !== null}
              >
                {socialLoading === "apple" ? (
                  <span>Signing in...</span>
                ) : (
                  <>
                    <FaApple className="h-4 w-4" />
                    <span>Sign in with Apple</span>
                  </>
                )}
              </Button>
            </div>

            <div className="flex items-center gap-2 mt-4 mb-2">
              <Separator className="flex-1" />
              <span className="text-xs text-muted-foreground">OR</span>
              <Separator className="flex-1" />
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type="password"
                          placeholder="••••••••"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full" disabled={isLoading || socialLoading !== null}>
                  {isLoading ? "Signing in..." : "Sign In"}
                </Button>
              </form>
            </Form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4">
            <div className="text-center text-sm">
              <Link
                href="/forgot-password"
                prefetch={true}
                className="text-blue-600 hover:text-blue-800"
              >
                Forgot your password?
              </Link>
            </div>
            <div className="text-center text-sm">
              Don&apos;t have an account?{" "}
              <Link
                href="/register"
                prefetch={true}
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                Sign Up
              </Link>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div className="container mx-auto px-4 py-12 flex justify-center">Loading...</div>}>
      <LoginForm />
    </Suspense>
  );
}
