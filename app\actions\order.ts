'use server'

import { executeTransaction } from '@/lib/db-utils'
import { getUserInfo } from '@/lib/db-utils'
import { uploadBase64ImageToBlob } from '@/lib/utils'
import { PoolClient } from 'pg'
import { v4 as uuidv4 } from 'uuid'
import { sendOrderConfirmation } from './email'

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image: string
  customDesignId?: string
  customDesignData?: string
}

interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
}

export async function createOrder({
  userId,
  items,
  deliveryMethod,
  deliveryAddress,
  pickupLocation,
  specialInstructions,
  subtotal,
  deliveryFee,
  promotionalDiscount,
  totalAmount,
}: {
  userId: string
  items: OrderItem[]
  deliveryMethod: 'PICKUP' | 'DELIVERY'
  deliveryAddress?: DeliveryAddress
  pickupLocation?: string
  specialInstructions?: string
  subtotal: number
  deliveryFee: number
  promotionalDiscount: number
  totalAmount: number
}) {
  console.log('Starting order creation process', { userId, itemCount: items.length, deliveryMethod, totalAmount });

  let customDesignImageUrl: string | null = null;

  // Upload custom design image to Vercel Blob if it exists (outside transaction)
  if (items[0]?.customDesignData) {
    try {
      console.log('Uploading custom design image to Vercel Blob...');
      const fileName = `custom-designs/${uuidv4()}.png`;
      customDesignImageUrl = await uploadBase64ImageToBlob(items[0].customDesignData, fileName);
      console.log("Successfully uploaded custom design to Vercel Blob:", customDesignImageUrl);
    } catch (error) {
      console.error("Failed to upload custom design to Vercel Blob:", error);
      // Continue with order creation even if image upload fails
      // In production, we might want to retry or use a fallback
    }
  }

  // Use executeTransaction for safer connection management
  try {
    const result = await executeTransaction(async (client) => {
      console.log('Starting database transaction for order creation');

      const user = await getUserInfo(client, userId);
      const actualUserId = user.id;
      console.log('Retrieved user info', { actualUserId, userEmail: user.email });

      const getNextOrderNumber = async (client: PoolClient) => {
        // Use a more efficient approach with sequence or atomic increment
        const result = await client.query(
          `SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 5) AS INTEGER)), 0) + 1 as next_number
           FROM orders
           WHERE order_number ~ '^ORD-[0-9]+$'`
        )
        const nextNumber = result.rows[0].next_number
        return `ORD-${nextNumber.toString().padStart(6, '0')}`
      }

      const orderNumber = await getNextOrderNumber(client)
      console.log('Generated order number:', orderNumber);

      const orderResult = await client.query(
        `INSERT INTO orders (
          user_id, order_number, total_amount, delivery_method,
          delivery_fee, promotional_discount, special_instructions, custom_design_image_url, pickup_location
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) RETURNING id`,
        [
          actualUserId,
          orderNumber,
          totalAmount,
          deliveryMethod,
          deliveryFee,
          promotionalDiscount,
          specialInstructions || null,
          customDesignImageUrl,
          deliveryMethod === 'PICKUP' ? pickupLocation : null
        ]
      )

      console.log('Order inserted successfully', { orderId: orderResult.rows[0].id });

      const orderId = orderResult.rows[0].id

      // Batch insert order items for better performance
      if (items.length > 0) {
        console.log('Inserting order items', { itemCount: items.length });
        const orderItemsValues = items.map((item) => [
          orderId,
          item.id,
          Array.isArray(item.name) ? item.name[0] : item.name,
          item.quantity,
          item.price,
          item.price * item.quantity
        ]);

        const placeholders = orderItemsValues.map((_, index) =>
          `($${index * 6 + 1}, $${index * 6 + 2}, $${index * 6 + 3}, $${index * 6 + 4}, $${index * 6 + 5}, $${index * 6 + 6})`
        ).join(', ');

        const flatValues = orderItemsValues.flat();

        await client.query(
          `INSERT INTO order_items (
            order_id, product_id, product_name,
            quantity, price_at_order, item_subtotal
          ) VALUES ${placeholders}`,
          flatValues
        );
        console.log('Order items inserted successfully');
      }

      if (deliveryMethod === 'DELIVERY' && deliveryAddress) {
        console.log('Inserting delivery address');
        await client.query(
          `INSERT INTO delivery_addresses (
            order_id, street, city, province, postal_code
          ) VALUES ($1, $2, $3, $4, $5)`,
          [
            orderId,
            deliveryAddress.street,
            deliveryAddress.city,
            deliveryAddress.province,
            deliveryAddress.postalCode
          ]
        )
        console.log('Delivery address inserted successfully');
      }

      // Get estimated delivery time based on delivery method
      const currentDate = new Date()
      const estimatedDeliveryTime = deliveryMethod === 'DELIVERY'
        ? new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000).toLocaleDateString() // 2 days for delivery
        : '30 minutes for pickup'

      console.log('Database transaction completed successfully, preparing to send email');

      // Return order data - email will be sent outside transaction
      return {
        success: true,
        orderId: orderId.toString(),
        orderNumber,
        user,
        items,
        subtotal,
        promotionalDiscount,
        deliveryFee,
        totalAmount,
        deliveryMethod,
        deliveryAddress,
        pickupLocation: deliveryMethod === 'PICKUP' ? pickupLocation : undefined,
        estimatedDeliveryTime,
        specialInstructions,
        customDesignImageUrl
      }
    }); // End of executeTransaction

    // Send email outside of transaction to avoid blocking database operations
    try {
      console.log("Sending order confirmation email with custom design URL:", result.customDesignImageUrl);
      await sendOrderConfirmation({
        orderNumber: result.orderNumber,
        items: result.items,
        subtotalAmount: result.subtotal,
        promotionalDiscount: result.promotionalDiscount,
        hasPromotionalItems: result.promotionalDiscount > 0,
        deliveryFee: result.deliveryFee,
        totalAmount: result.totalAmount,
        deliveryMethod: result.deliveryMethod,
        deliveryAddress: result.deliveryAddress,
        pickupLocation: result.pickupLocation,
        estimatedDeliveryTime: result.estimatedDeliveryTime,
        customerName: result.user.name || 'Valued Customer',
        customerEmail: result.user.email,
        customerPhone: result.user.phone || '',
        specialInstructions: result.specialInstructions,
        customDesignImageUrl: result.customDesignImageUrl
      })
      console.log('Order confirmation email sent successfully');
    } catch (emailError) {
      console.error('Error sending order confirmation email:', emailError);
    }

    // Return simplified response
    return {
      success: result.success,
      orderId: result.orderId,
      orderNumber: result.orderNumber
    };

  } catch (error) {
    console.error('Order creation failed:', error);

    // Check if it's a database connection error
    if (error && typeof error === 'object' && 'message' in error) {
      const errorMessage = (error as Error).message;
      if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
        console.error('Database connection limit reached during order creation');
        throw new Error('Service temporarily unavailable. Please try again in a moment.');
      }
      if (errorMessage.includes('connection') || errorMessage.includes('timeout')) {
        console.error('Database connection issue during order creation');
        throw new Error('Connection issue. Please try again.');
      }
    }

    throw error;
  }
}