import bcrypt from "bcryptjs"
import { UserType } from "./auth"
import { safeQuery } from "./db"

interface DatabaseUser {
  id: string;
  name: string;
  email: string;
  password: string;
  user_type: UserType;
}

export async function saltAndHashPassword(password: string): Promise<string> {
  const salt = await bcrypt.genSalt(10)
  return bcrypt.hash(password, salt)
}

export async function getUserFromDb(email: string, password: string) {
  try {
    // Use safeQuery to avoid connection management issues
    const result = await safeQuery(
      'SELECT id, name, email, password, user_type FROM users WHERE email = $1',
      [email]
    )

    if (result.rows.length > 0) {
      const user = result.rows[0] as DatabaseUser
      const isPasswordValid = await bcrypt.compare(password, user.password)

      if (isPasswordValid) {
        return {
          id: user.id,
          name: user.name,
          email: user.email,
          userType: user.user_type as UserType,
        }
      }
    }

    return null
  } catch (error) {
    console.error('Error fetching user:', error)

    // Check if it's a connection error
    if (error && typeof error === 'object' && 'code' in error) {
      const pgError = error as { code: string; message: string };
      if (pgError.code === '53300') {
        console.error('Database connection limit reached during authentication');
      }
    }

    return null
  }
}