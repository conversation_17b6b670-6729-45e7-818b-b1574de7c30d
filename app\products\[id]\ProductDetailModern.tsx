"use client";

import Image from "next/image";
import Link from "next/link";
import { ArrowLeft, Star, ShoppingCart } from "lucide-react";
import { useState } from "react";
import DesignDialog from "@/components/DesignDialog";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

interface Product {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  created_at?: string;
}

interface ProductDetailProps {
  product: Product;
}

console.log("modern");

const extractHighlights = (longDescription: string | string[]): string[] => {
  if (!longDescription) return [];
  const descriptionText = Array.isArray(longDescription)
    ? longDescription.join("\n")
    : longDescription;
  if (descriptionText.includes("|")) {
    return descriptionText
      .split("|")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  if (descriptionText.includes("\n")) {
    return descriptionText
      .split("\n")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  return descriptionText
    .split(/[.!?]/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

const CheckoutSection = ({ product }: ProductDetailProps) => {
  const [isDesignDialogOpen, setIsDesignDialogOpen] = useState(false);
  const router = useRouter();

  const handleContinueToCheckout = () => {
    setIsDesignDialogOpen(true);
  };

  const handleDesignComplete = (designId: string) => {
    localStorage.setItem(
      "selectedProduct",
      JSON.stringify({
        id: product.id,
        title: product.title,
        price: product.price,
        image_url: product.image_url,
      })
    );
    router.push(`/checkout?product=${product.id}&designId=${designId}`);
  };

  return (
    <>
      <Button
        onClick={handleContinueToCheckout}
        size="lg"
        className="w-full bg-emerald-600 hover:bg-emerald-700 mt-6"
      >
        <ShoppingCart className="mr-2 h-5 w-5" />
        Buy Now
      </Button>
      <DesignDialog
        isOpen={isDesignDialogOpen}
        onClose={() => setIsDesignDialogOpen(false)}
        productId={product.id.toString()}
        productName={product.title}
        onDesignComplete={handleDesignComplete}
      />
    </>
  );
};

const ProductDetailModern = ({ product }: ProductDetailProps) => {
  const [selectedImage, setSelectedImage] = useState(product.image_url);
  const productHighlights = extractHighlights(product.long_description);

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-white to-emerald-50">
      <div className="container mx-auto px-4 md:px-8 py-12">
        <div className="flex flex-col md:flex-row gap-12">
          {/* Left: Images */}
          <div className="md:w-1/2 flex flex-col gap-6">
            <div className="relative w-full h-[400px] rounded-xl overflow-hidden shadow-lg border border-emerald-200 bg-white">
              <Image
                src={selectedImage}
                alt={product.title}
                fill
                className="object-cover"
                priority
              />
            </div>
            <div className="flex gap-3">
              {[product.image_url, ...(product.gallery_urls || [])].map(
                (img) => (
                  <button
                    key={img}
                    onClick={() => setSelectedImage(img)}
                    className={`relative w-20 h-20 rounded-lg overflow-hidden border-2 ${
                      selectedImage === img
                        ? "border-emerald-600"
                        : "border-gray-200"
                    }`}
                  >
                    <Image
                      src={img}
                      alt={`${product.title} thumbnail`}
                      fill
                      className="object-cover"
                    />
                  </button>
                )
              )}
            </div>
          </div>
          {/* Right: Details */}
          <div className="md:w-1/2 flex flex-col gap-8">
            <div>
              <Link
                href="/products"
                prefetch={true}
                className="flex items-center gap-1.5 px-3 py-1.5 text-sm bg-white text-green-700 hover:bg-green-100 border border-green-600 rounded-md transition-colors duration-200 mb-4 w-fit"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Back to Products</span>
              </Link>
              <h1 className="text-4xl font-bold mb-2 text-emerald-700">
                {product.title}
              </h1>
              <p className="text-lg text-gray-700 mb-4">
                {product.short_description}
              </p>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>By</span>
                <span className="uppercase font-semibold text-emerald-700">
                  PRINTCLOUD DESIGN
                </span>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-emerald-700">
                Product Highlights
              </h3>
              <ul className="space-y-2 text-sm">
                {productHighlights.map((highlight, idx) => (
                  <li key={idx} className="flex items-start">
                    <span className="bg-emerald-100 text-emerald-600 rounded-full p-1 mr-2 flex-shrink-0">
                      <Star className="h-3 w-3" />
                    </span>
                    <span>{highlight}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-emerald-700">
                Description
              </h3>
              <p className="text-gray-800 whitespace-pre-line">
                {Array.isArray(product.long_description)
                  ? product.long_description.join("\n")
                  : product.long_description}
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2 text-emerald-700">Price</h3>
              <div className="text-3xl font-bold text-emerald-600 mb-1">
                $
                {typeof product.price === "string"
                  ? parseFloat(product.price).toFixed(2)
                  : product.price.toFixed(2)}{" "}
                CAD
              </div>
              <div className="text-sm text-gray-500">
                Free shipping on orders over $50
              </div>
            </div>
            <CheckoutSection product={product} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailModern;
