import { cache } from 'react'

// Cache orders data for 1 hour (3600 seconds)
export const fetchOrders = cache(async () => {
  const response = await fetch('/api/dashboard/orders', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',
    next: { 
      revalidate: 3600,
      tags: ['dashboard-data', 'orders-data']
    }
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error("API Response Error:", response.status, errorText)
    throw new Error(`HTTP ${response.status}: ${errorText || "Failed to fetch orders"}`)
  }

  const data = await response.json()
  return data
})

// Cache user profile data for 1 hour
export const fetchUserProfile = cache(async () => {
  const response = await fetch('/api/dashboard/profile', {
    method: 'GET',
    headers: { 'Content-Type': 'application/json' },
    credentials: 'include',
    next: { 
      revalidate: 3600,
      tags: ['dashboard-data', 'profile-data']
    }
  })

  if (!response.ok) {
    throw new Error('Failed to fetch profile')
  }

  const data = await response.json()
  return data
})

// Cache products data for 5 minutes (300 seconds)
export const fetchProducts = cache(async (categoryId?: string, sortBy?: string) => {
  let url = '/api/products'
  const params = new URLSearchParams()
  
  if (categoryId) {
    params.append('category', categoryId)
  }
  
  if (sortBy) {
    params.append('sort', sortBy)
  }
  
  if (params.toString()) {
    url += `?${params.toString()}`
  }
  
  const response = await fetch(url, {
    next: { 
      revalidate: 300,
      tags: ['products-data']
    }
  })

  if (!response.ok) {
    throw new Error('Failed to fetch products')
  }

  const data = await response.json()
  return data.products || []
}) 