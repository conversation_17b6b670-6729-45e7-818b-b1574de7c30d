import { Skeleton } from "@/components/ui/skeleton";
import { CheckCircle2 } from "lucide-react";

export function OrderSuccessSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8 mt-[120px]">
      <div className="max-w-4xl mx-auto">
        {/* Order Confirmation Card */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Skeleton className="h-8 w-60 mb-2" />
              <Skeleton className="h-5 w-40" />
            </div>
            <div className="flex items-center opacity-40">
              <CheckCircle2 className="w-6 h-6 mr-2" />
              <Skeleton className="h-5 w-24" />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="flex items-start">
              <Skeleton className="w-5 h-5 mr-3 rounded-full" />
              <div>
                <Skeleton className="h-5 w-32 mb-2" />
                <Skeleton className="h-4 w-48" />
              </div>
            </div>

            <div className="flex items-start">
              <Skeleton className="w-5 h-5 mr-3 rounded-full" />
              <div>
                <Skeleton className="h-5 w-32 mb-2" />
                <Skeleton className="h-4 w-48" />
              </div>
            </div>
          </div>

          {/* Delivery Address Skeleton */}
          <div className="mb-6">
            <Skeleton className="h-5 w-36 mb-2" />
            <Skeleton className="h-4 w-60 mb-1" />
            <Skeleton className="h-4 w-56 mb-1" />
            <Skeleton className="h-4 w-28" />
          </div>
        </div>

        {/* Order Summary Card */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <Skeleton className="h-7 w-40 mb-6" />
          
          {/* Order Items */}
          <div className="space-y-4 mb-6">
            {/* Item 1 */}
            <div className="flex items-center gap-4">
              <Skeleton className="w-16 h-16 rounded-lg" />
              <div className="flex-1">
                <Skeleton className="h-5 w-40 mb-1" />
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
            
            {/* Item 2 */}
            <div className="flex items-center gap-4">
              <Skeleton className="w-16 h-16 rounded-lg" />
              <div className="flex-1">
                <Skeleton className="h-5 w-56 mb-1" />
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-5 w-20" />
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="border-t pt-4 space-y-2">
            <div className="flex justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="flex justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="flex justify-between pt-2 border-t">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-20" />
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="mt-8 flex justify-center gap-4">
          <Skeleton className="h-10 w-40" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
    </div>
  );
} 