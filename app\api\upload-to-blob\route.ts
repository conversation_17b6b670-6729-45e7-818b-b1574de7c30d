import { NextResponse } from "next/server";
import { put } from "@vercel/blob"; 

export async function POST(request: Request) {
  const formData = await request.formData();
  const file = formData.get("file") as File;
  if (!file) return NextResponse.json({ error: "No file" }, { status: 400 });

  const arrayBuffer = await file.arrayBuffer();
  const buffer = Buffer.from(arrayBuffer);

  // Add addRandomSuffix: true to avoid filename collisions
  const blob = await put(file.name, buffer, { access: "public", addRandomSuffix: true });
  return NextResponse.json({ url: blob.url });
}