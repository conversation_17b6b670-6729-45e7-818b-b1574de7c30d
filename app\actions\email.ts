'use server'

import { sendEmail } from '@/lib/email-service'

interface OrderItem {
  name: string
  quantity: number
  price: number
  image: string
  customDesignId?: string
}

interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
}

export async function sendOrderConfirmation({
  orderNumber,
  items,
  subtotalAmount,
  promotionalDiscount,
  hasPromotionalItems,
  deliveryFee,
  totalAmount,
  deliveryMethod,
  deliveryAddress,
  pickupLocation,
  estimatedDeliveryTime,
  customerName,
  customerEmail,
  customerPhone,
  specialInstructions,
  customDesignImageUrl,
}: {
  orderNumber: string
  items: OrderItem[]
  subtotalAmount: number
  promotionalDiscount: number
  hasPromotionalItems: boolean
  deliveryFee: number
  totalAmount: number
  deliveryMethod: 'PICKUP' | 'DELIVERY'
  deliveryAddress?: DeliveryAddress
  pickupLocation?: string
  estimatedDeliveryTime: string
  customerName: string
  customerEmail: string
  customerPhone: string
  specialInstructions?: string
  customDesignImageUrl?: string | null
}) {
  try {
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; font-size: 14px;">
        <h1 style="color: #2563eb; font-size: 20px; margin-bottom: 10px;">Order Confirmation</h1>
        <p>Thank you for your order!</p>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px; background-color: #f8fafc; border-radius: 8px;">
          <tr>
            <td style="padding: 10px;">
              <h2 style="color: #1e40af; font-size: 16px; margin-top: 0;">Order Details</h2>
              <p><strong>Order Number:</strong> ${orderNumber}</p>
              <p><strong>Delivery Method:</strong> ${deliveryMethod}</p>
              ${deliveryMethod === 'DELIVERY' && deliveryAddress ? `
                <p><strong>Delivery Address:</strong><br>
                ${deliveryAddress.street}<br>
                ${deliveryAddress.city}, ${deliveryAddress.province}<br>
                ${deliveryAddress.postalCode}</p>
              ` : ''}
              ${deliveryMethod === 'PICKUP' && pickupLocation ? `
                <p><strong>Pickup Location:</strong><br>
                ${pickupLocation}</p>
              ` : ''}
              <p><strong>Estimated ${deliveryMethod === 'DELIVERY' ? 'Delivery' : 'Pickup'} Time:</strong> ${estimatedDeliveryTime}</p>
            </td>
          </tr>
        </table>

        <h2 style="color: #1e40af; font-size: 16px;">Order Items</h2>
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px;">
          ${items.map(item => `
            <tr>
              <td style="padding: 10px; background-color: #f8fafc; border-radius: 4px; margin-bottom: 10px;">
                <table style="width: 100%;">
                  <tr>
                    <td style="width: 60px; vertical-align: top;">
                      ${item.image ? `<img src="${item.image}" alt="${item.name}" style="width: 50px; height: 50px; object-fit: cover; border-radius: 4px;">` : ''}
                    </td>
                    <td style="vertical-align: top;">
                      <p style="margin: 0 0 5px 0;"><strong>${item.name}</strong></p>
                      <p style="margin: 0 0 5px 0;">Quantity: ${item.quantity}</p>
                      <p style="margin: 0 0 5px 0;">Price: $${Number(item.price).toFixed(2)}</p>
                      <p style="margin: 0;">Subtotal: $${(Number(item.price) * item.quantity).toFixed(2)}</p>
                    </td>
                  </tr>
                  ${item.customDesignId ? `
                    <tr>
                      <td colspan="2" style="padding-top: 10px;">
                        <p style="margin: 0 0 5px 0;"><strong>Custom Design Included</strong></p>
                        ${customDesignImageUrl ? `
                          <img src="${customDesignImageUrl}" alt="Custom Design" style="max-width: 100%; max-height: 150px; object-fit: contain; border-radius: 4px;">
                        ` : `
                          <p style="margin: 0; color: #6b7280;">Your custom design will be applied to your order</p>
                        `}
                      </td>
                    </tr>
                  ` : ''}
                </table>
              </td>
            </tr>
          `).join('')}
        </table>

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px; background-color: #f8fafc; border-radius: 8px;">
          <tr>
            <td style="padding: 10px;">
              <h2 style="color: #1e40af; font-size: 16px; margin-top: 0;">Order Summary</h2>
              <table style="width: 100%;">
                <tr>
                  <td>Subtotal:</td>
                  <td style="text-align: right;">$${subtotalAmount.toFixed(2)}</td>
                </tr>
                ${hasPromotionalItems ? `
                <tr>
                  <td>Promotional Discount:</td>
                  <td style="text-align: right;">-$${promotionalDiscount.toFixed(2)}</td>
                </tr>
                ` : ''}
                ${deliveryMethod === 'DELIVERY' ? `
                <tr>
                  <td>Delivery Fee:</td>
                  <td style="text-align: right;">$${deliveryFee.toFixed(2)}</td>
                </tr>
                ` : ''}
                <tr>
                  <td style="font-weight: bold; padding-top: 5px; border-top: 1px solid #e5e7eb;">Total Amount:</td>
                  <td style="font-weight: bold; text-align: right; padding-top: 5px; border-top: 1px solid #e5e7eb;">$${totalAmount.toFixed(2)}</td>
                </tr>
              </table>
            </td>
          </tr>
        </table>

        ${specialInstructions ? `
          <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px; background-color: #f8fafc; border-radius: 8px;">
            <tr>
              <td style="padding: 10px;">
                <h2 style="color: #1e40af; font-size: 16px; margin-top: 0;">Special Instructions</h2>
                <p style="margin: 0;">${specialInstructions}</p>
              </td>
            </tr>
          </table>
        ` : ''}

        <table style="width: 100%; border-collapse: collapse; margin-bottom: 15px; background-color: #f8fafc; border-radius: 8px;">
          <tr>
            <td style="padding: 10px;">
              <h2 style="color: #1e40af; font-size: 16px; margin-top: 0;">Customer Information</h2>
              <p style="margin: 0 0 5px 0;"><strong>Name:</strong> ${customerName}</p>
              <p style="margin: 0 0 5px 0;"><strong>Email:</strong> ${customerEmail}</p>
              ${customerPhone ? `<p style="margin: 0;"><strong>Phone:</strong> ${customerPhone}</p>` : ''}
            </td>
          </tr>
        </table>

        <div style="margin: 15px 0; text-align: center; font-size: 12px; color: #6b7280;">
          <p style="margin: 0 0 5px 0;">If you have any questions about your order, please contact us at:</p>
          <p style="margin: 0 0 5px 0;">Phone: ${process.env.NEXT_PUBLIC_PHONE_NO || '************'}</p>
          <p style="margin: 0;">Email: ${process.env.EMAIL_USER || '<EMAIL>'}</p>
        </div>
      </div>
    `;

    // Use the utility function to send the email
    return await sendEmail({
      to: customerEmail,
      subject: `Order Confirmation - ${orderNumber}`,
      html,
      from: process.env.EMAIL_USER
    });
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error };
  }
} 