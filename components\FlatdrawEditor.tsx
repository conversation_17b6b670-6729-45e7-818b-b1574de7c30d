"use client";

import { useState, useRef, useEffect, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { getStroke } from "perfect-freehand";
import {
  Type,
  Pencil,
  Eraser,
  Image as ImageIcon,
  Save,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "lucide-react";

// Type definitions
type Point = [number, number, number?]; // x, y, pressure (optional)
type DrawingMode = "draw" | "erase" | "text" | "image" | "select" | "ai-text";

interface PathObject {
  id: string;
  points: Point[];
  strokeWidth: number;
  strokeColor: string;
  mode: DrawingMode;
  text?: string;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
}

interface FlatdrawEditorProps {
  initialWidth?: number;
  initialHeight?: number;
  backgroundImage?: string;
  backgroundColor?: string;
  productImage?: string;
  onSave?: (data: string) => void;
  productId?: string; // Added productId for context-aware AI prompting
  productName?: string; // Added product name for better AI prompts
  productType?: string; // Added product type for better AI prompts
}

export default function FlatdrawEditor({
  initialWidth = 800,
  initialHeight = 600,
  backgroundImage,
  backgroundColor = "#ffffff",
  productImage,
  onSave,
  productId,
  productName,
  productType,
}: FlatdrawEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [mode, setMode] = useState<DrawingMode>("select");
  const [strokeWidth, setStrokeWidth] = useState(5);
  const [strokeColor, setStrokeColor] = useState("#000000");
  const [paths, setPaths] = useState<PathObject[]>([]);
  const [currentPath, setCurrentPath] = useState<PathObject | null>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [canvasSize, setCanvasSize] = useState({
    width: initialWidth,
    height: initialHeight,
  });
  const [bgColor, setBgColor] = useState(backgroundColor);
  const [textInput, setTextInput] = useState<{
    active: boolean;
    value: string;
    x: number;
    y: number;
    editMode: "inline" | "popup";
    fontSize: number;
    pathId?: string;
  }>({
    active: false,
    value: "",
    x: 0,
    y: 0,
    editMode: "inline",
    fontSize: 16,
    pathId: undefined,
  });
  const inputRef = useRef<HTMLInputElement>(null);
  const [history, setHistory] = useState<PathObject[][]>([]);
  const [future, setFuture] = useState<PathObject[][]>([]);

  // Selection state
  const [selectedPathId, setSelectedPathId] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const [isEditing, setIsEditing] = useState(false);

  // Add resize state
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<
    "topLeft" | "topRight" | "bottomLeft" | "bottomRight" | null
  >(null);
  const [resizeOrigin, setResizeOrigin] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });

  // AI text generation state
  const [isAIPromptOpen, setIsAIPromptOpen] = useState(false);
  const [aiPrompt, setAIPrompt] = useState("");
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [aiGeneratedText, setAIGeneratedText] = useState("");

  // Helper function to get the canvas coordinates
  const getCoordinates = (
    e: React.MouseEvent | React.TouchEvent | MouseEvent | TouchEvent
  ): [number, number, number?] => {
    const canvas = canvasRef.current;
    if (!canvas) return [0, 0];

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    if ("touches" in e) {
      const touch = e.touches[0];
      const pressure = 0.5; // Fixed pressure value
      return [
        (touch.clientX - rect.left) * scaleX,
        (touch.clientY - rect.top) * scaleY,
        pressure,
      ];
    } else {
      return [
        (e.clientX - rect.left) * scaleX,
        (e.clientY - rect.top) * scaleY,
        0.5,
      ];
    }
  };

  // Helper functions for selection
  const isPointInTextBounds = (
    point: [number, number],
    path: PathObject
  ): boolean => {
    if (
      path.mode !== "text" ||
      !path.text ||
      path.x === undefined ||
      path.y === undefined
    )
      return false;

    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return false;

    ctx.font = `${path.strokeWidth * 5}px sans-serif`;
    const textMetrics = ctx.measureText(path.text);
    const textHeight = path.strokeWidth * 5;

    const padding = 10;

    return (
      point[0] >= path.x - padding &&
      point[0] <= path.x + textMetrics.width + padding &&
      point[1] >= path.y - textHeight - padding &&
      point[1] <= path.y + padding
    );
  };

  const isPointInImageBounds = (
    point: [number, number],
    path: PathObject
  ): boolean => {
    if (
      path.mode !== "image" ||
      path.x === undefined ||
      path.y === undefined ||
      !path.width ||
      !path.height
    )
      return false;

    return (
      point[0] >= path.x &&
      point[0] <= path.x + path.width &&
      point[1] >= path.y &&
      point[1] <= path.y + path.height
    );
  };

  const findPathAtPoint = (point: Point): PathObject | null => {
    // Check from last (top) to first (bottom)
    for (let i = paths.length - 1; i >= 0; i--) {
      const path = paths[i];
      // Get just the x,y coordinates for checking bounds
      const pointXY: [number, number] = [point[0], point[1]];
      if (path.mode === "text" && isPointInTextBounds(pointXY, path)) {
        return path;
      } else if (path.mode === "image" && isPointInImageBounds(pointXY, path)) {
        return path;
      }
    }
    return null;
  };

  // Enhanced text editing interface component
  const TextEditPopup = () => {
    const [localTextColor, setLocalTextColor] = useState(() => {
      if (textInput.pathId) {
        const selectedPath = paths.find((p) => p.id === textInput.pathId);
        return selectedPath?.strokeColor || strokeColor;
      }
      return strokeColor;
    });

    if (!textInput.active || textInput.editMode !== "popup") return null;

    const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setTextInput({ ...textInput, value: e.target.value });
    };

    const handleFontSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const size = parseInt(e.target.value);
      if (!isNaN(size)) {
        setTextInput({ ...textInput, fontSize: size });
      }
    };

    const handleColorChange = (color: string) => {
      setLocalTextColor(color);
    };

    const handleConfirm = () => {
      // Apply the selected color before calling applyTextEdit
      setStrokeColor(localTextColor);
      applyTextEdit(localTextColor);
    };

    const handleCancel = () => {
      setTextInput({
        active: false,
        value: "",
        x: 0,
        y: 0,
        editMode: "inline",
        fontSize: 16,
        pathId: undefined,
      });
      setIsEditing(false);
    };

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1000]">
        <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
          <h3 className="text-lg font-bold mb-4">Edit Text</h3>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              Text Content
            </label>
            <textarea
              value={textInput.value}
              onChange={handleTextChange}
              className="w-full border rounded-md p-2 min-h-[100px]"
              placeholder="Enter your text here..."
              autoFocus
            />
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Font Size</label>
            <div className="flex items-center gap-2">
              <span className="text-sm">{textInput.fontSize}px</span>
              <input
                type="range"
                min="8"
                max="72"
                value={textInput.fontSize}
                onChange={handleFontSizeChange}
                className="flex-1"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">Text Color</label>
            <div className="flex items-center gap-2">
              <input
                type="color"
                value={localTextColor}
                onChange={(e) => handleColorChange(e.target.value)}
                className="w-10 h-10"
              />
              <div className="grid grid-cols-6 gap-2 flex-1">
                {[
                  "#000000",
                  "#ff0000",
                  "#00ff00",
                  "#0000ff",
                  "#ffff00",
                  "#ff00ff",
                ].map((color) => (
                  <div
                    key={color}
                    className={`h-8 w-8 rounded-full cursor-pointer border ${
                      localTextColor === color
                        ? "ring-2 ring-offset-2 ring-blue-500"
                        : ""
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorChange(color)}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="button" onClick={handleConfirm}>
              Confirm
            </Button>
          </div>
        </div>
      </div>
    );
  };

  // Handle mouse/touch down
  const handlePointerDown = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();
    if (textInput.active) return;

    const point = getCoordinates(e);

    if (mode === "select") {
      // Check if we're clicking on a resize handle of a selected image
      if (selectedPathId) {
        const selectedPath = paths.find((p) => p.id === selectedPathId);
        if (selectedPath && selectedPath.mode === "image") {
          const clickedHandle = getClickedResizeHandle(point, selectedPath);
          if (clickedHandle) {
            // Start resizing
            setIsResizing(true);
            setResizeHandle(clickedHandle);

            // Set the resize origin (the opposite corner that stays fixed)
            if (clickedHandle === "topLeft") {
              setResizeOrigin({
                x: (selectedPath.x || 0) + (selectedPath.width || 0),
                y: (selectedPath.y || 0) + (selectedPath.height || 0),
              });
            } else if (clickedHandle === "topRight") {
              setResizeOrigin({
                x: selectedPath.x || 0,
                y: (selectedPath.y || 0) + (selectedPath.height || 0),
              });
            } else if (clickedHandle === "bottomLeft") {
              setResizeOrigin({
                x: (selectedPath.x || 0) + (selectedPath.width || 0),
                y: selectedPath.y || 0,
              });
            } else if (clickedHandle === "bottomRight") {
              setResizeOrigin({
                x: selectedPath.x || 0,
                y: selectedPath.y || 0,
              });
            }

            return;
          }
        }
      }

      const pathAtPoint = findPathAtPoint(point);

      if (pathAtPoint) {
        // Select the path
        setSelectedPathId(pathAtPoint.id);
        setIsDragging(true);

        // Calculate drag offset for smoother dragging
        setDragOffset({
          x: point[0] - (pathAtPoint.x || 0),
          y: point[1] - (pathAtPoint.y || 0),
        });
      } else {
        // Deselect if clicking outside
        setSelectedPathId(null);
      }
      return;
    }

    if (mode === "text") {
      setTextInput({
        active: true,
        value: "",
        x: point[0],
        y: point[1],
        editMode: "inline",
        fontSize: 16,
        pathId: undefined,
      });
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
      return;
    }

    setIsDrawing(true);

    const newPath: PathObject = {
      id: Date.now().toString(),
      points: [point],
      strokeWidth,
      strokeColor,
      mode,
    };

    setCurrentPath(newPath);
  };

  // Handle mouse/touch move
  const handlePointerMove = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();

    const point = getCoordinates(e);

    if (mode === "select") {
      // Handle image resizing
      if (isResizing && selectedPathId && resizeHandle) {
        const selectedPathIndex = paths.findIndex(
          (p) => p.id === selectedPathId
        );
        if (selectedPathIndex >= 0) {
          const selectedPath = paths[selectedPathIndex];

          if (selectedPath.mode === "image") {
            const updatedPaths = [...paths];
            let newX = selectedPath.x || 0;
            let newY = selectedPath.y || 0;
            let newWidth = selectedPath.width || 0;
            let newHeight = selectedPath.height || 0;

            // Calculate new dimensions based on which handle was dragged
            switch (resizeHandle) {
              case "topLeft":
                newWidth = Math.max(30, resizeOrigin.x - point[0]);
                newHeight = Math.max(30, resizeOrigin.y - point[1]);
                newX = resizeOrigin.x - newWidth;
                newY = resizeOrigin.y - newHeight;
                break;

              case "topRight":
                newWidth = Math.max(30, point[0] - resizeOrigin.x);
                newHeight = Math.max(30, resizeOrigin.y - point[1]);
                newX = resizeOrigin.x;
                newY = resizeOrigin.y - newHeight;
                break;

              case "bottomLeft":
                newWidth = Math.max(30, resizeOrigin.x - point[0]);
                newHeight = Math.max(30, point[1] - resizeOrigin.y);
                newX = resizeOrigin.x - newWidth;
                newY = resizeOrigin.y;
                break;

              case "bottomRight":
                newWidth = Math.max(30, point[0] - resizeOrigin.x);
                newHeight = Math.max(30, point[1] - resizeOrigin.y);
                newX = resizeOrigin.x;
                newY = resizeOrigin.y;
                break;
            }

            updatedPaths[selectedPathIndex] = {
              ...selectedPath,
              x: newX,
              y: newY,
              width: newWidth,
              height: newHeight,
            };

            setPaths(updatedPaths);
          }
        }
        return;
      }

      // Handle dragging
      if (isDragging && selectedPathId) {
        // Find the selected path
        const updatedPaths = paths.map((path) => {
          if (path.id === selectedPathId) {
            return {
              ...path,
              x: point[0] - dragOffset.x,
              y: point[1] - dragOffset.y,
            };
          }
          return path;
        });

        setPaths(updatedPaths);
        return;
      }

      // If in select mode, change cursor when hovering over resize handle
      if (selectedPathId && !isDragging && !isResizing) {
        const selectedPath = paths.find((p) => p.id === selectedPathId);
        if (selectedPath && selectedPath.mode === "image") {
          const handle = getClickedResizeHandle(point, selectedPath);
          const canvas = canvasRef.current;
          if (canvas) {
            if (handle === "topLeft" || handle === "bottomRight") {
              canvas.style.cursor = "nwse-resize";
            } else if (handle === "topRight" || handle === "bottomLeft") {
              canvas.style.cursor = "nesw-resize";
            } else {
              canvas.style.cursor = "default";
            }
          }
        } else {
          const canvas = canvasRef.current;
          if (canvas) {
            canvas.style.cursor = "default";
          }
        }
      }
    }

    if (!isDrawing || !currentPath || textInput.active) return;

    if (["draw", "erase"].includes(currentPath.mode)) {
      setCurrentPath({
        ...currentPath,
        points: [...currentPath.points, point],
      });
    }
  };

  // Handle mouse/touch up
  const handlePointerUp = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault();

    if (mode === "select") {
      // End resizing
      if (isResizing) {
        setIsResizing(false);

        // Add to history
        setHistory([...history, paths]);
        setFuture([]);
        return;
      }

      // End dragging
      if (isDragging) {
        setIsDragging(false);

        // Add to history
        setHistory([...history, paths]);
        setFuture([]);
        return;
      }
    }

    if (!isDrawing || !currentPath || textInput.active) return;

    setIsDrawing(false);

    if (
      ["draw", "erase"].includes(currentPath.mode) &&
      currentPath.points.length < 2
    ) {
      // If it's just a dot, add another close point
      const point = getCoordinates(e);
      setCurrentPath({
        ...currentPath,
        points: [
          ...currentPath.points,
          [point[0] + 0.1, point[1] + 0.1, point[2]],
        ],
      });
    }

    // Add to history
    setHistory([...history, paths]);
    setFuture([]);

    setPaths([...paths, currentPath]);
    setCurrentPath(null);
  };

  // Handle text input
  const handleTextInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTextInput({ ...textInput, value: e.target.value });
  };

  // Handle text input keydown
  const handleTextInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      addTextToCanvas();
    } else if (e.key === "Escape") {
      // Cancel text input
      setTextInput({
        active: false,
        value: "",
        x: 0,
        y: 0,
        editMode: "inline",
        fontSize: 16,
        pathId: undefined,
      });
      setIsEditing(false);
    }
  };

  // Add text to canvas
  const addTextToCanvas = () => {
    if (textInput.value.trim()) {
      if (isEditing && selectedPathId) {
        // Update existing text
        const updatedPaths = paths.map((path) => {
          if (path.id === selectedPathId) {
            return {
              ...path,
              text: textInput.value,
            };
          }
          return path;
        });

        // Add to history
        setHistory([...history, paths]);
        setFuture([]);

        setPaths(updatedPaths);
      } else {
        // Create new text
        const newText: PathObject = {
          id: Date.now().toString(),
          points: [],
          strokeWidth,
          strokeColor,
          mode: "text",
          text: textInput.value,
          x: textInput.x,
          y: textInput.y,
        };

        // Add to history
        setHistory([...history, paths]);
        setFuture([]);

        setPaths([...paths, newText]);
      }
    }

    setTextInput({
      active: false,
      value: "",
      x: 0,
      y: 0,
      editMode: "inline",
      fontSize: 16,
      pathId: undefined,
    });
    setIsEditing(false);
  };

  // Apply text edits from popup editor
  const applyTextEdit = (textColor?: string) => {
    if (!textInput.value.trim() || !textInput.pathId) return;

    // Update existing text with the new value, font size, and color
    const updatedPaths = paths.map((path) => {
      if (path.id === textInput.pathId) {
        // Calculate stroke width from font size (reverse of what we do when displaying)
        const newStrokeWidth = textInput.fontSize / 5;

        return {
          ...path,
          text: textInput.value,
          strokeWidth: newStrokeWidth,
          strokeColor: textColor || path.strokeColor, // Use the new color if provided
        };
      }
      return path;
    });

    // Add to history
    setHistory([...history, paths]);
    setFuture([]);

    setPaths(updatedPaths);

    // Reset text input state
    setTextInput({
      active: false,
      value: "",
      x: 0,
      y: 0,
      editMode: "inline",
      fontSize: 16,
      pathId: undefined,
    });
    setIsEditing(false);
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    if (!file.type.startsWith("image/")) return;

    const reader = new FileReader();
    reader.onload = (event) => {
      const result = event.target?.result;
      if (event.target && result !== null && typeof result === "string") {
        const img = new Image();
        img.src = result;
        img.onload = () => {
          // Scale down if needed
          const maxWidth = canvasSize.width * 0.8;
          const maxHeight = canvasSize.height * 0.8;

          let width = img.width;
          let height = img.height;

          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }

          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }

          // Center on canvas
          const x = (canvasSize.width - width) / 2;
          const y = (canvasSize.height - height) / 2;

          const newImage: PathObject = {
            id: Date.now().toString(),
            points: [],
            strokeWidth: 1,
            strokeColor: "transparent",
            mode: "image",
            x,
            y,
            width,
            height,
            text: result,
          };

          // Add to history
          setHistory([...history, paths]);
          setFuture([]);

          setPaths([...paths, newImage]);
        };
      }
    };

    reader.readAsDataURL(file);
  };

  // Undo
  const handleUndo = () => {
    if (history.length === 0) return;

    const previousPaths = history[history.length - 1];
    setFuture([...future, paths]);
    setHistory(history.slice(0, -1));
    setPaths(previousPaths);
  };

  // Redo
  const handleRedo = () => {
    if (future.length === 0) return;

    const nextPaths = future[future.length - 1];
    setHistory([...history, paths]);
    setFuture(future.slice(0, -1));
    setPaths(nextPaths);
  };

  // Clear canvas
  const handleClear = () => {
    if (paths.length === 0) return;

    // Add to history before clearing
    setHistory([...history, paths]);
    setFuture([]);
    setPaths([]);
  };

  // Save canvas
  const handleSave = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // If there's an external save handler, call it
    if (onSave) {
      onSave(canvas.toDataURL("image/png"));
      return;
    }

    // Otherwise download the image
    const link = document.createElement("a");
    link.download = "canvas-design.png";
    link.href = canvas.toDataURL("image/png");
    link.click();
  };

  // Resize canvas based on container size
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const width = Math.min(containerRef.current.clientWidth, initialWidth);
        const height = (initialHeight * width) / initialWidth;
        setCanvasSize({ width, height });
      }
    };

    updateCanvasSize();
    window.addEventListener("resize", updateCanvasSize);
    return () => window.removeEventListener("resize", updateCanvasSize);
  }, [initialWidth, initialHeight]);

  // Move drawCanvas to the top using useCallback
  const drawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext("2d");
    if (!canvas || !ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Set background color
    ctx.fillStyle = bgColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw background image if any (but not the product image, which is handled as a path)
    if (backgroundImage) {
      const img = new Image();
      img.src = backgroundImage;
      if (img.complete) {
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      } else {
        img.onload = () => {
          ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        };
      }
    }

    // Draw all paths
    paths.forEach((path) => {
      if (path.mode === "draw") {
        ctx.fillStyle = path.strokeColor;

        // Use perfect-freehand for smooth drawing
        const stroke = getStroke(
          path.points.map((point) => ({
            x: point[0],
            y: point[1],
            pressure: point[2] || 0.5,
          })),
          {
            size: path.strokeWidth,
            thinning: 0.5,
            smoothing: 0.5,
            streamline: 0.5,
          }
        );

        if (stroke.length > 0) {
          ctx.beginPath();
          const [x0, y0] = stroke[0];
          ctx.moveTo(x0, y0);

          for (let i = 1; i < stroke.length; i++) {
            const [x, y] = stroke[i];
            ctx.lineTo(x, y);
          }

          ctx.fill();
        }
      } else if (path.mode === "erase") {
        const stroke = getStroke(
          path.points.map((point) => ({
            x: point[0],
            y: point[1],
            pressure: point[2] || 0.5,
          })),
          {
            size: path.strokeWidth + 2, // Make eraser slightly bigger
            thinning: 0.5,
            smoothing: 0.5,
            streamline: 0.5,
          }
        );

        if (stroke.length > 0) {
          ctx.save();
          ctx.globalCompositeOperation = "destination-out";
          ctx.beginPath();
          const [x0, y0] = stroke[0];
          ctx.moveTo(x0, y0);

          for (let i = 1; i < stroke.length; i++) {
            const [x, y] = stroke[i];
            ctx.lineTo(x, y);
          }

          ctx.fill();
          ctx.restore();
        }
      } else if (
        path.mode === "text" &&
        path.text &&
        path.x !== undefined &&
        path.y !== undefined
      ) {
        const x = path.x;
        const y = path.y;

        ctx.fillStyle = path.strokeColor;
        ctx.font = `${path.strokeWidth * 5}px sans-serif`;
        ctx.fillText(path.text, x, y);

        // Debug text bounds if enabled
        const customWindow = window as { __debugTextBounds?: boolean };
        if (customWindow.__debugTextBounds) {
          const textMetrics = ctx.measureText(path.text);
          const textHeight = path.strokeWidth * 5;
          const padding = 10;

          ctx.save();
          ctx.strokeStyle = "rgba(255, 0, 0, 0.5)";
          ctx.lineWidth = 1;
          ctx.strokeRect(
            x - padding,
            y - textHeight - padding,
            textMetrics.width + padding * 2,
            textHeight + padding * 2
          );
          ctx.restore();
        }

        // Highlight selected text element
        if (selectedPathId === path.id && mode === "select" && !isEditing) {
          const textMetrics = ctx.measureText(path.text);
          const textHeight = path.strokeWidth * 5;

          ctx.save();
          ctx.strokeStyle = "#3b82f6"; // Blue highlight
          ctx.lineWidth = 1;
          ctx.setLineDash([5, 3]);
          ctx.strokeRect(
            x - 5,
            y - textHeight,
            textMetrics.width + 10,
            textHeight + 10
          );

          // Draw handle for dragging
          ctx.fillStyle = "#3b82f6";
          ctx.fillRect(x - 5, y - textHeight - 5, 10, 10);
          ctx.restore();
        }
      } else if (
        path.mode === "image" &&
        path.text &&
        path.x !== undefined &&
        path.y !== undefined &&
        path.width &&
        path.height
      ) {
        const x = path.x;
        const y = path.y;
        const width = path.width;
        const height = path.height;

        const img = new Image();
        img.src = path.text;
        if (img.complete) {
          ctx.drawImage(img, x, y, width, height);
        } else {
          img.onload = () => {
            ctx.drawImage(img, x, y, width, height);
          };
        }

        // Highlight selected image
        if (selectedPathId === path.id && mode === "select") {
          ctx.save();
          ctx.strokeStyle = "#3b82f6"; // Blue highlight
          ctx.lineWidth = 1;
          ctx.setLineDash([5, 3]);
          ctx.strokeRect(x, y, width, height);

          // Draw handle for dragging at the center
          ctx.fillStyle = "#3b82f6";
          ctx.fillRect(x + width / 2 - 5, y + height / 2 - 5, 10, 10);

          // Draw resize handles at all four corners
          ctx.fillRect(x - 5, y - 5, 10, 10); // Top-left
          ctx.fillRect(x + width - 5, y - 5, 10, 10); // Top-right
          ctx.fillRect(x - 5, y + height - 5, 10, 10); // Bottom-left
          ctx.fillRect(x + width - 5, y + height - 5, 10, 10); // Bottom-right

          ctx.restore();
        }
      }
    });

    // Draw current path while drawing
    if (currentPath && isDrawing) {
      if (currentPath.mode === "draw") {
        ctx.fillStyle = currentPath.strokeColor;

        const stroke = getStroke(
          currentPath.points.map((point) => ({
            x: point[0],
            y: point[1],
            pressure: point[2] || 0.5,
          })),
          {
            size: currentPath.strokeWidth,
            thinning: 0.5,
            smoothing: 0.5,
            streamline: 0.5,
          }
        );

        if (stroke.length > 0) {
          ctx.beginPath();
          const [x0, y0] = stroke[0];
          ctx.moveTo(x0, y0);

          for (let i = 1; i < stroke.length; i++) {
            const [x, y] = stroke[i];
            ctx.lineTo(x, y);
          }

          ctx.fill();
        }
      } else if (currentPath.mode === "erase") {
        const stroke = getStroke(
          currentPath.points.map((point) => ({
            x: point[0],
            y: point[1],
            pressure: point[2] || 0.5,
          })),
          {
            size: currentPath.strokeWidth + 2,
            thinning: 0.5,
            smoothing: 0.5,
            streamline: 0.5,
          }
        );

        if (stroke.length > 0) {
          ctx.save();
          ctx.globalCompositeOperation = "destination-out";
          ctx.beginPath();
          const [x0, y0] = stroke[0];
          ctx.moveTo(x0, y0);

          for (let i = 1; i < stroke.length; i++) {
            const [x, y] = stroke[i];
            ctx.lineTo(x, y);
          }

          ctx.fill();
          ctx.restore();
        }
      }
    }
  }, [
    paths,
    currentPath,
    isDrawing,
    bgColor,
    backgroundImage,
    selectedPathId,
    mode,
    isEditing,
    canvasRef,
  ]);

  // Delete selected element with useCallback
  const deleteSelectedElement = useCallback(() => {
    if (!selectedPathId) return;

    // Filter out the selected path
    const updatedPaths = paths.filter((path) => path.id !== selectedPathId);

    // Add to history
    setHistory([...history, paths]);
    setFuture([]); // This resets future, doesn't depend on it

    // Update paths and clear selection
    setPaths(updatedPaths);
    setSelectedPathId(null);
  }, [
    selectedPathId,
    paths,
    history,
    setHistory,
    setFuture,
    setPaths,
    setSelectedPathId,
  ]);

  // Update canvas when things change
  useEffect(() => {
    drawCanvas();
  }, [drawCanvas]);

  // Handle keyboard events for deleting elements
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Delete selected element when Delete key or Backspace is pressed
      if (
        (e.key === "Delete" || e.key === "Backspace") &&
        selectedPathId &&
        !textInput.active &&
        !isAIPromptOpen
      ) {
        e.preventDefault();

        deleteSelectedElement();
      }
    };

    // Add event listener
    window.addEventListener("keydown", handleKeyDown);

    // Clean up
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedPathId, textInput.active, isAIPromptOpen, deleteSelectedElement]);

  // Add a function to improve the text bounding box visualization for debugging
  useEffect(() => {
    // This effect runs once after initial render
    // Add a function to the window for debugging purposes
    if (typeof window !== "undefined") {
      // Create an interface to extend Window for our debug property
      interface CustomWindow extends Window {
        debugTextBounds?: (enabled?: boolean) => void;
        __debugTextBounds?: boolean;
      }

      const customWindow = window as CustomWindow;

      customWindow.debugTextBounds = (enabled = true) => {
        customWindow.__debugTextBounds = enabled;
        drawCanvas(); // Redraw to show/hide debug info
      };
    }

    return () => {
      // Clean up
      if (typeof window !== "undefined") {
        const customWindow = window as { debugTextBounds?: unknown };
        delete customWindow.debugTextBounds;
      }
    };
  }, [drawCanvas]);

  // AI text generation function - enhanced with better product context
  const generateAIText = async (prompt: string) => {
    try {
      // Create context-aware prompt based on product details
      let contextPrompt = prompt;

      if (productType || productName) {
        // Build a more detailed context string
        let contextString = "";

        if (productType && productName) {
          contextString = `${productType} called "${productName}"`;
        } else if (productType) {
          contextString = productType;
        } else if (productName) {
          contextString = `"${productName}"`;
        }

        if (productId) {
          contextString += ` (ID: ${productId})`;
        }

        contextPrompt = `For a ${contextString}: ${prompt}`;
      }

      // Simple mock API call for demo - in production, replace with actual API
      const response = await fetch("/api/generate-text", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt: contextPrompt,
          productId,
          productType,
          productName,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API error:", errorText);
        throw new Error(
          `Failed to generate text: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      return data.text;
    } catch (error) {
      console.error("Error generating AI text:", error);
      // Fallback for demo purposes - remove in production
      return productName
        ? `AI-generated text for "${prompt}" related to ${productName} (${
            productType || "product"
          })`
        : `AI-generated text for "${prompt}" related to this ${
            productType || "product"
          }`;
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // Handle AI text generation submission
  const handleAITextSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!aiPrompt.trim()) return;

    try {
      setIsGeneratingAI(true);
      const generatedText = await generateAIText(aiPrompt);

      // Store the generated text for display in the modal
      setAIGeneratedText(generatedText);

      // After generating, place text on canvas at center
      const x = canvasSize.width / 2;
      const y = canvasSize.height / 2;

      // Create new text element
      const newText: PathObject = {
        id: Date.now().toString(),
        points: [],
        strokeWidth,
        strokeColor,
        mode: "text",
        text: generatedText,
        x,
        y,
      };

      // Add to history
      setHistory([...history, paths]);
      setFuture([]);

      // Add to canvas
      setPaths([...paths, newText]);

      // Close prompt dialog
      setIsAIPromptOpen(false);
      setAIPrompt("");

      // Select the new text element
      setSelectedPathId(newText.id);
    } catch (error) {
      console.error("Error handling AI text submission:", error);
      alert("Failed to generate text. Please try again.");
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // Handle double click for text editing with popup
  const handleDoubleClick = (e: React.MouseEvent) => {
    if (mode !== "select") {
      return;
    }

    const point = getCoordinates(e);

    // First check if there's a selected path and it's a text element
    if (selectedPathId) {
      const selectedPath = paths.find((p) => p.id === selectedPathId);
      if (selectedPath && selectedPath.mode === "text") {
        const fontSize = (selectedPath.strokeWidth || 5) * 5;

        // Start editing text
        setIsEditing(true);
        setTextInput({
          active: true,
          value: selectedPath.text || "",
          x: selectedPath.x || 0,
          y: selectedPath.y || 0,
          editMode: "popup",
          fontSize: fontSize,
          pathId: selectedPath.id,
        });
        return;
      }
    }

    // If no selected text element, try finding a text element at the click point
    const pathAtPoint = findPathAtPoint(point);

    if (pathAtPoint && pathAtPoint.mode === "text") {
      // Calculate font size from stroke width
      const fontSize = (pathAtPoint.strokeWidth || 5) * 5;

      // Start editing text
      setIsEditing(true);
      setTextInput({
        active: true,
        value: pathAtPoint.text || "",
        x: pathAtPoint.x || 0,
        y: pathAtPoint.y || 0,
        editMode: "popup", // Use popup mode
        fontSize: fontSize,
        pathId: pathAtPoint.id,
      });
      setSelectedPathId(pathAtPoint.id);
    }
  };

  // Get the specific resize handle that was clicked
  const getClickedResizeHandle = (
    point: Point,
    path: PathObject
  ): "topLeft" | "topRight" | "bottomLeft" | "bottomRight" | null => {
    if (
      path.mode !== "image" ||
      path.x === undefined ||
      path.y === undefined ||
      !path.width ||
      !path.height
    )
      return null;

    const topLeft = { x: path.x, y: path.y };
    const topRight = { x: path.x + path.width, y: path.y };
    const bottomLeft = { x: path.x, y: path.y + path.height };
    const bottomRight = { x: path.x + path.width, y: path.y + path.height };

    if (
      Math.abs(point[0] - topLeft.x) <= 15 &&
      Math.abs(point[1] - topLeft.y) <= 15
    ) {
      return "topLeft";
    } else if (
      Math.abs(point[0] - topRight.x) <= 15 &&
      Math.abs(point[1] - topRight.y) <= 15
    ) {
      return "topRight";
    } else if (
      Math.abs(point[0] - bottomLeft.x) <= 15 &&
      Math.abs(point[1] - bottomLeft.y) <= 15
    ) {
      return "bottomLeft";
    } else if (
      Math.abs(point[0] - bottomRight.x) <= 15 &&
      Math.abs(point[1] - bottomRight.y) <= 15
    ) {
      return "bottomRight";
    }

    return null;
  };

  // Add product image as a selectable element when canvas loads
  useEffect(() => {
    // Only add the product image if it exists and hasn't been added yet
    if (productImage && canvasRef.current && paths.every(path => path.text !== productImage)) {
      // Create an image element for the product
      const img = new Image();
      img.src = productImage;
      img.onload = () => {
        // Calculate dimensions to fit the canvas while maintaining aspect ratio
        const canvas = canvasRef.current;
        if (!canvas) return;

        const maxWidth = canvas.width * 0.8;
        const maxHeight = canvas.height * 0.8;
        
        let width = img.width;
        let height = img.height;
        
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
        
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
        
        // Center on canvas
        const x = (canvas.width - width) / 2;
        const y = (canvas.height - height) / 2;
        
        // Add the image as a selectable element in the paths
        const newProductImage: PathObject = {
          id: `product-image-${Date.now()}`,
          points: [],
          strokeWidth: 1,
          strokeColor: "transparent",
          mode: "image",
          x,
          y,
          width,
          height,
          text: productImage
        };
        
        // Add to paths
        setPaths(prevPaths => [...prevPaths, newProductImage]);
        
        // Select the image
        setSelectedPathId(newProductImage.id);
      };
    }
  }, [productImage, canvasRef, paths]);

  return (
    <>
      <div className="flex flex-col md:flex-row gap-4">
        {/* Tools Panel - Vertical on mobile, horizontal on desktop */}
        <div className="flex flex-col gap-4 w-full md:w-64">
          <div className="bg-gray-100 p-3 rounded-lg">
            <h3 className="text-sm font-medium mb-2">Drawing Tools</h3>
            <div className="grid grid-cols-3 xs:grid-cols-4 sm:grid-cols-6 md:grid-cols-2 gap-2">
              <button
                className={`flex flex-col items-center justify-center p-2 rounded-lg ${
                  mode === "select" ? "bg-blue-100 text-blue-600" : "bg-white"
                }`}
                onClick={() => setMode("select")}
                title="Select"
              >
                <MousePointer className="h-4 w-4" />
                <span className="text-xs mt-1">Select</span>
              </button>
              <button
                className={`flex flex-col items-center justify-center p-2 rounded-lg ${
                  mode === "draw" ? "bg-blue-100 text-blue-600" : "bg-white"
                }`}
                onClick={() => setMode("draw")}
                title="Draw"
              >
                <Pencil className="h-4 w-4" />
                <span className="text-xs mt-1">Draw</span>
              </button>
              <button
                className={`flex flex-col items-center justify-center p-2 rounded-lg ${
                  mode === "erase" ? "bg-blue-100 text-blue-600" : "bg-white"
                }`}
                onClick={() => setMode("erase")}
                title="Erase"
              >
                <Eraser className="h-4 w-4" />
                <span className="text-xs mt-1">Erase</span>
              </button>
              <button
                className={`flex flex-col items-center justify-center p-2 rounded-lg ${
                  mode === "text" ? "bg-blue-100 text-blue-600" : "bg-white"
                }`}
                onClick={() => setMode("text")}
                title="Add Text"
              >
                <Type className="h-4 w-4" />
                <span className="text-xs mt-1">Text</span>
              </button>
              <button
                className={`flex flex-col items-center justify-center p-2 rounded-lg ${
                  mode === "image" ? "bg-blue-100 text-blue-600" : "bg-white"
                }`}
                onClick={() => {
                  setMode("image");
                  const input = document.createElement("input");
                  input.type = "file";
                  input.accept = "image/*";
                  input.onchange = (e: Event) => {
                    const target = e.target as HTMLInputElement;
                    if (target && target.files) {
                      handleImageUpload({ target } as React.ChangeEvent<HTMLInputElement>);
                    }
                  };
                  input.click();
                }}
                title="Add Image"
              >
                <ImageIcon className="h-4 w-4" />
                <span className="text-xs mt-1">Image</span>
              </button>
              <button
                className={`flex flex-col items-center justify-center p-2 rounded-lg ${
                  mode === "ai-text" ? "bg-blue-100 text-blue-600" : "bg-white"
                }`}
                onClick={() => {
                  setMode("ai-text");
                  setIsAIPromptOpen(true);
                }}
                title="AI Text"
              >
                <Sparkles className="h-4 w-4" />
                <span className="text-xs mt-1">AI Text</span>
              </button>
            </div>
          </div>

          {/* Collapsible settings panel on mobile */}
          <div className="bg-gray-100 p-3 rounded-lg">
            <details className="md:hidden">
              <summary className="font-medium text-sm mb-2 cursor-pointer">
                Drawing Settings
              </summary>
              <div className="pt-2">
                {mode === "draw" && (
                  <>
                    <label className="text-xs text-gray-600 mb-1 block">
                      Line Width: {strokeWidth}px
                    </label>
                    <Slider
                      value={[strokeWidth]}
                      min={1}
                      max={20}
                      step={1}
                      onValueChange={(value) => setStrokeWidth(value[0])}
                      className="mb-4"
                    />
                  </>
                )}
                {(mode === "draw" || mode === "text") && (
                  <>
                    <label className="text-xs text-gray-600 mb-1 block">
                      Color
                    </label>
                    <div className="flex flex-wrap gap-1">
                      {[
                        "#000000",
                        "#FF0000",
                        "#00FF00",
                        "#0000FF",
                        "#FFFF00",
                        "#FF00FF",
                        "#00FFFF",
                        "#FFFFFF",
                      ].map((color) => (
                        <div
                          key={color}
                          className={`w-6 h-6 rounded-full cursor-pointer border border-gray-300 ${
                            strokeColor === color
                              ? "ring-2 ring-offset-1 ring-blue-500"
                              : ""
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => setStrokeColor(color)}
                        />
                      ))}
                    </div>
                  </>
                )}
                <div className="mt-3">
                  <label className="text-xs text-gray-600 mb-1 block">
                    Background Color
                  </label>
                  <div className="flex flex-wrap gap-1">
                    {[
                      "#FFFFFF",
                      "#F0F0F0",
                      "#FFE4E1",
                      "#E6F2FF",
                      "#F0FFF0",
                      "#FFF8DC",
                    ].map((color) => (
                      <div
                        key={color}
                        className={`w-6 h-6 rounded-full cursor-pointer border border-gray-300 ${
                          bgColor === color
                            ? "ring-2 ring-offset-1 ring-blue-500"
                            : ""
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setBgColor(color)}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </details>

            {/* Always expanded on desktop */}
            <div className="hidden md:block">
              <h3 className="text-sm font-medium mb-2">Drawing Settings</h3>
              {mode === "draw" && (
                <>
                  <label className="text-xs text-gray-600 mb-1 block">
                    Line Width: {strokeWidth}px
                  </label>
                  <Slider
                    value={[strokeWidth]}
                    min={1}
                    max={20}
                    step={1}
                    onValueChange={(value) => setStrokeWidth(value[0])}
                    className="mb-4"
                  />
                </>
              )}
              {(mode === "draw" || mode === "text") && (
                <>
                  <label className="text-xs text-gray-600 mb-1 block">
                    Color
                  </label>
                  <div className="flex flex-wrap gap-1">
                    {[
                      "#000000",
                      "#FF0000",
                      "#00FF00",
                      "#0000FF",
                      "#FFFF00",
                      "#FF00FF",
                      "#00FFFF",
                      "#FFFFFF",
                    ].map((color) => (
                      <div
                        key={color}
                        className={`w-6 h-6 rounded-full cursor-pointer border border-gray-300 ${
                          strokeColor === color
                            ? "ring-2 ring-offset-1 ring-blue-500"
                            : ""
                        }`}
                        style={{ backgroundColor: color }}
                        onClick={() => setStrokeColor(color)}
                      />
                    ))}
                  </div>
                </>
              )}
              <div className="mt-3">
                <label className="text-xs text-gray-600 mb-1 block">
                  Background Color
                </label>
                <div className="flex flex-wrap gap-1">
                  {[
                    "#FFFFFF",
                    "#F0F0F0",
                    "#FFE4E1",
                    "#E6F2FF",
                    "#F0FFF0",
                    "#FFF8DC",
                  ].map((color) => (
                    <div
                      key={color}
                      className={`w-6 h-6 rounded-full cursor-pointer border border-gray-300 ${
                        bgColor === color
                          ? "ring-2 ring-offset-1 ring-blue-500"
                          : ""
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => setBgColor(color)}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-100 p-3 rounded-lg">
            <h3 className="text-sm font-medium mb-2">Actions</h3>
            <div className="grid grid-cols-2 gap-2 mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleUndo}
                disabled={history.length === 0}
                title="Undo"
                className="w-full"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                <span className="text-xs">Undo</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRedo}
                disabled={future.length === 0}
                title="Redo"
                className="w-full"
              >
                <RotateCcw className="h-4 w-4 mr-1 transform scale-x-[-1]" />
                <span className="text-xs">Redo</span>
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleClear}
                className="text-red-500 hover:text-red-700 w-full"
                disabled={paths.length === 0}
                title="Clear All"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                <span className="text-xs">Clear</span>
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={handleSave}
                title="Save Image"
                className="w-full"
              >
                <Save className="h-4 w-4 mr-1" />
                <span className="text-xs">Save</span>
              </Button>
            </div>
          </div>
        </div>

        <div className="flex-1" ref={containerRef}>
          <div className="relative bg-white border rounded-lg shadow-md overflow-hidden">
            <canvas
              ref={canvasRef}
              width={canvasSize.width}
              height={canvasSize.height}
              className="touch-none w-full h-auto"
              onMouseDown={handlePointerDown}
              onMouseMove={handlePointerMove}
              onMouseUp={handlePointerUp}
              onMouseLeave={handlePointerUp}
              onTouchStart={handlePointerDown}
              onTouchMove={handlePointerMove}
              onTouchEnd={handlePointerUp}
              onDoubleClick={handleDoubleClick}
            />

            {textInput.active && textInput.editMode === "inline" && (
              <div
                style={{
                  position: "absolute",
                  left: textInput.x,
                  top: textInput.y - 20,
                  zIndex: 100,
                }}
              >
                <input
                  ref={inputRef}
                  type="text"
                  value={textInput.value}
                  onChange={handleTextInputChange}
                  onKeyDown={handleTextInputKeyDown}
                  onBlur={addTextToCanvas}
                  className="border border-blue-500 px-2 py-1 text-sm outline-none"
                  style={{ color: strokeColor }}
                  autoFocus
                />
              </div>
            )}

            {/* Delete button for selected elements */}
            {mode === "select" && selectedPathId && (
              <div className="absolute bottom-4 right-4 z-10">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={deleteSelectedElement}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Element
                </Button>
              </div>
            )}

            {/* AI text prompt modal */}
            {isAIPromptOpen && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-lg mx-2">
                  <h3 className="text-lg font-bold mb-4">Generate AI Text</h3>

                  <form onSubmit={handleAITextSubmit}>
                    <div className="mb-4">
                      <label className="block text-sm mb-2">
                        What text would you like to generate?
                        {productType && (
                          <span className="text-gray-500 ml-1">
                            (For {productType})
                          </span>
                        )}
                      </label>
                      <textarea
                        value={aiPrompt}
                        onChange={(e) => setAIPrompt(e.target.value)}
                        className="w-full border rounded-md p-2 min-h-[100px]"
                        placeholder={`E.g., "Create a catchy tagline for ${
                          productType || "this product"
                        }"`}
                        disabled={isGeneratingAI}
                      />
                      {productType && (
                        <p className="text-xs text-gray-500 mt-1">
                          Your prompt will be customized for this {productType}{" "}
                          product.
                        </p>
                      )}
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setIsAIPromptOpen(false)}
                        disabled={isGeneratingAI}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        disabled={!aiPrompt.trim() || isGeneratingAI}
                        className="flex items-center gap-2"
                      >
                        {isGeneratingAI ? (
                          <>
                            <span className="animate-spin">↻</span>
                            Generating...
                          </>
                        ) : (
                          <>
                            <Sparkles className="h-4 w-4" />
                            Generate
                          </>
                        )}
                      </Button>
                    </div>

                    {aiGeneratedText && !isGeneratingAI && (
                      <div className="mt-4 p-3 bg-gray-50 rounded border">
                        <p className="font-medium">Generated Text:</p>
                        <p className="mt-1">{aiGeneratedText}</p>
                      </div>
                    )}
                  </form>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Render TextEditPopup at root level */}
      <TextEditPopup />
    </>
  );
}
