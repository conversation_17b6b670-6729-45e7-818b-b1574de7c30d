'use client'

import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'

const FlatdrawEditor = dynamic(() => import('./FlatdrawEditor'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-[600px] border border-gray-300 rounded-lg flex items-center justify-center">
      Loading canvas editor...
    </div>
  ),
})

interface ProductCanvasProps {
  productId: string
  productName?: string
  productType?: string
}

const ProductCanvas = ({ 
  productId,
  productName: initialProductName,
  productType: initialProductType
}: ProductCanvasProps) => {
  const [productImage, setProductImage] = useState<string | null>(null)
  const [productInfo, setProductInfo] = useState({
    name: initialProductName || '',
    type: initialProductType || ''
  })

  useEffect(() => {
    const fetchProductDetails = async () => {
      try {
        const response = await fetch(`/api/products/${productId}`)
        if (!response.ok) {
          throw new Error('Failed to fetch product')
        }
        const data = await response.json()
        setProductImage(data.image_url || null)
        
        // Update product info if not provided via props
        if (!initialProductName || !initialProductType) {
          setProductInfo({
            name: data.name || '',
            type: data.type || data.category || ''
          })
        }
      } catch (error) {
        console.error('Error fetching product:', error)
      }
    }

    fetchProductDetails()
  }, [productId, initialProductName, initialProductType])

  const handleSaveDesign = async (dataUrl: string) => {
    try {
      // Here you can implement the logic to save the design
      // For example, you can send it to your API
      const response = await fetch('/api/designs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          designData: dataUrl,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to save design')
      }

      // You can show a success message or handle the response
      const result = await response.json()
      console.log('Design saved successfully:', result)
    } catch (error) {
      console.error('Error saving design:', error)
      // Handle error (show error message to user)
    }
  }

  return (
    <div className="mt-8">
      <h2 className="text-2xl font-bold mb-4">
        Customize Your Design
        {productInfo.name && (
          <span className="text-lg font-normal ml-2 text-gray-600">
            for {productInfo.name}
          </span>
        )}
      </h2>
      <FlatdrawEditor
        initialWidth={800}
        initialHeight={600}
        backgroundImage={productImage || undefined}
        backgroundColor="#ffffff"
        onSave={handleSaveDesign}
        productId={productId}
        productName={productInfo.name}
        productType={productInfo.type}
      />
    </div>
  )
}

export default ProductCanvas 