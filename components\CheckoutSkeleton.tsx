import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

export function CheckoutSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8 mt-[120px]">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-2 mb-8">
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-600 hover:text-blue-600 opacity-50 pointer-events-none"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Product
          </Button>
        </div>

        <h1 className="text-2xl md:text-3xl font-bold mb-8">Checkout</h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary Skeleton */}
          <Card className="bg-white rounded-lg shadow-lg p-6">
            <Skeleton className="h-7 w-40 mb-6" />
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <Skeleton className="w-16 h-16 rounded-lg" />
                <div className="flex-1">
                  <Skeleton className="h-5 w-40 mb-2" />
                  <Skeleton className="h-4 w-20 mb-1" />
                  <Skeleton className="h-5 w-24" />
                </div>
              </div>

              <div className="pt-4 space-y-2 border-t">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <div className="pt-4 border-t">
                  <div className="flex justify-between">
                    <Skeleton className="h-6 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Delivery Form Skeleton */}
          <Card className="bg-white rounded-lg shadow-lg p-6">
            <Skeleton className="h-7 w-48 mb-6" />
            <div className="space-y-6">
              <div className="flex gap-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>

              <div className="space-y-4">
                <div>
                  <Skeleton className="h-4 w-32 mb-1" />
                  <Skeleton className="h-10 w-full" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Skeleton className="h-4 w-16 mb-1" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <div>
                    <Skeleton className="h-4 w-24 mb-1" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                </div>

                <div>
                  <Skeleton className="h-4 w-28 mb-1" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>

              <div>
                <Skeleton className="h-4 w-48 mb-1" />
                <Skeleton className="h-24 w-full" />
              </div>

              <Skeleton className="h-20 w-full rounded-md" />

              <Skeleton className="h-12 w-full rounded-md" />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
} 