import { NextResponse } from 'next/server';
import { dbCircuitBreaker } from '@/lib/db-circuit-breaker';
import { safeQuery } from '@/lib/db';

export async function GET() {
  try {
    const circuitBreakerStatus = dbCircuitBreaker.getStatus();
    
    // Basic health check
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      circuitBreaker: circuitBreakerStatus,
      database: {
        connected: false,
        responseTime: 0
      }
    };

    // Test database connection if circuit breaker allows
    if (circuitBreakerStatus.canExecute) {
      try {
        const startTime = Date.now();
        await safeQuery('SELECT 1 as health_check');
        const endTime = Date.now();
        
        healthCheck.database.connected = true;
        healthCheck.database.responseTime = endTime - startTime;
      } catch (error) {
        console.error('Database health check failed:', error);
        healthCheck.database.connected = false;
        healthCheck.status = 'degraded';
      }
    } else {
      healthCheck.status = 'degraded';
      healthCheck.database.connected = false;
    }

    const statusCode = healthCheck.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(healthCheck, { status: statusCode });
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Health check failed'
    }, { status: 503 });
  }
}
