# Database Connection Pool Fixes

## Problem Analysis

The error "remaining connection slots are reserved for roles with the SUPERUSER attribute" indicates your database has reached its maximum connection limit. This is typically caused by:

1. **Connection Pool Exhaustion**: Too many connections being created
2. **Connection Leaks**: Connections not being properly released
3. **Multiple Pool Instances**: Different parts of the app creating separate pools
4. **High Pool Limits**: Pool max set too high for database capacity

## Root Causes Identified

### 1. Pool Configuration Issues
- **Before**: Pool max was set to 20 connections
- **Problem**: Most managed databases (like Neon) have limited connections (often 10-20 total)
- **Solution**: Reduced to 5 connections max

### 2. Multiple Pool Instances
- **Problem**: Some files use `getPool()`, others use default export
- **Risk**: Creates multiple pool instances, multiplying connection usage

### 3. Missing Connection Cleanup
- **Problem**: Some API routes don't properly release connections in error scenarios
- **Risk**: Connections remain open indefinitely

### 4. Inadequate Monitoring
- **Problem**: No visibility into connection usage
- **Risk**: Issues go undetected until failure

## Solutions Implemented

### 1. Optimized Pool Configuration (`lib/db.ts`)

```typescript
// Conservative settings to prevent exhaustion
max: 5, // Reduced from 20
idleTimeoutMillis: 10000, // Faster cleanup
connectionTimeoutMillis: 3000, // Fail fast
allowExitOnIdle: false, // Prevent pool recreation
```

### 2. Enhanced Connection Monitoring

```typescript
// Added pool event listeners
pool.on('acquire', () => console.log('Client acquired'));
pool.on('release', () => console.log('Client released'));
pool.on('error', (err) => {
  console.error('Pool error:', err);
  logConnectionStats();
});
```

### 3. Connection Health Monitoring (`lib/connection-monitor.ts`)

New utilities for monitoring and debugging:
- `getPoolStats()`: Real-time pool statistics
- `getDatabaseConnectionInfo()`: Database-level connection info
- `monitorConnections()`: Comprehensive monitoring
- `checkForConnectionLeaks()`: Detect long-running queries
- `emergencyCleanup()`: Emergency diagnostics

### 4. Improved Error Handling

```typescript
// Better session timeouts
SET statement_timeout = '15s';
SET lock_timeout = '5s';
SET idle_in_transaction_session_timeout = '30s';
```

## Usage Instructions

### 1. Monitor Connection Health

```typescript
import { monitorConnections, getConnectionHealth } from '@/lib/connection-monitor';

// Check current status
const health = await getConnectionHealth();
console.log('Status:', health.status);
console.log('Issues:', health.issues);
console.log('Recommendations:', health.recommendations);

// Start continuous monitoring
const monitor = startConnectionMonitoring(30000); // Every 30 seconds
```

### 2. Debug Connection Issues

```typescript
import { checkForConnectionLeaks, emergencyCleanup } from '@/lib/connection-monitor';

// Check for leaks
const leaks = await checkForConnectionLeaks();
if (leaks.hasLeaks) {
  console.log('Long-running queries found:', leaks.longRunningQueries);
}

// Emergency diagnostics
await emergencyCleanup();
```

### 3. Proper Connection Usage Pattern

```typescript
// ✅ GOOD: Using pool.query() (auto-releases)
const pool = getPool();
const result = await pool.query('SELECT * FROM users WHERE id = $1', [userId]);

// ✅ GOOD: Manual connection with proper cleanup
const pool = getPool();
const client = await pool.connect();
try {
  await client.query('BEGIN');
  const result = await client.query('SELECT * FROM users WHERE id = $1', [userId]);
  await client.query('COMMIT');
  return result;
} catch (error) {
  await client.query('ROLLBACK');
  throw error;
} finally {
  client.release(); // CRITICAL: Always release
}

// ❌ BAD: Missing client.release()
const client = await pool.connect();
const result = await client.query('SELECT * FROM users');
// Missing: client.release() - CAUSES LEAK!
```

## Immediate Actions Required

### 1. Update Environment Variables
Ensure your database can handle the connections:
```env
# For Neon or similar managed databases
DATABASE_URL=your_connection_string
# Consider connection pooling services like PgBouncer
```

### 2. Add Monitoring to Your App

Add this to your main application startup:

```typescript
// In your main app file (e.g., app/layout.tsx or pages/_app.tsx)
import { startConnectionMonitoring } from '@/lib/connection-monitor';

// Start monitoring in development
if (process.env.NODE_ENV === 'development') {
  startConnectionMonitoring(30000);
}
```

### 3. Review All Database Usage

Check these files for proper connection handling:
- `app/api/*/route.ts` - All API routes
- `app/actions/*.ts` - Server actions
- Any custom database utilities

## Prevention Strategies

### 1. Connection Limits
- **Pool Max**: Keep ≤ 50% of database max connections
- **Timeouts**: Use aggressive timeouts to fail fast
- **Monitoring**: Continuous connection monitoring

### 2. Code Patterns
- **Prefer `pool.query()`**: Auto-releases connections
- **Always use try/finally**: Ensure `client.release()` is called
- **Avoid long transactions**: Keep transactions short
- **Use connection pooling**: Consider PgBouncer for production

### 3. Database Configuration
- **Connection Limits**: Monitor database connection limits
- **Query Timeouts**: Set reasonable statement timeouts
- **Idle Cleanup**: Configure idle connection cleanup

## Troubleshooting Commands

### Check Current Connections
```sql
-- See all connections to your database
SELECT pid, usename, application_name, client_addr, state, query_start, query 
FROM pg_stat_activity 
WHERE datname = current_database();

-- Count connections by state
SELECT state, COUNT(*) 
FROM pg_stat_activity 
WHERE datname = current_database() 
GROUP BY state;

-- Find long-running queries
SELECT pid, now() - pg_stat_activity.query_start AS duration, query, state
FROM pg_stat_activity 
WHERE datname = current_database()
  AND state != 'idle'
  AND now() - pg_stat_activity.query_start > interval '30 seconds';
```

### Emergency Connection Cleanup
```sql
-- Kill specific connection (use with caution)
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE datname = current_database() 
  AND pid != pg_backend_pid()
  AND state = 'idle'
  AND now() - state_change > interval '5 minutes';
```

## Expected Results

After implementing these fixes:
- ✅ Connection pool exhaustion should be eliminated
- ✅ Better visibility into connection usage
- ✅ Faster failure detection and recovery
- ✅ Improved application stability
- ✅ Better resource utilization

## Monitoring Dashboard

Consider adding a simple monitoring endpoint:

```typescript
// app/api/health/connections/route.ts
import { getConnectionHealth } from '@/lib/connection-monitor';

export async function GET() {
  const health = await getConnectionHealth();
  return Response.json(health);
}
```

This provides a health check endpoint you can monitor or alert on.
