"use client";

import { useState, useMemo } from "react";
import Image from "next/image";
import Link from "next/link";
import {
  ArrowLeft,
  ShoppingCart,
  Star,
  Check,
  Heart,
  Share2,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "next/navigation";
import DesignDialog from "@/components/DesignDialog";

interface Product {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  created_at?: string;
}

console.log("modern2");

interface ProductDetailProps {
  product: Product;
}

const ProductGalleryThumbnails = ({
  product,
  selectedImage,
  setSelectedImage,
}: ProductDetailProps & {
  selectedImage: string;
  setSelectedImage: (img: string) => void;
}) => (
  <div className="space-y-4">
    <div className="grid grid-cols-4 gap-3">
      {[product.image_url, ...(product.gallery_urls || [])].map(
        (img, index) => (
          <button
            key={img}
            onClick={() => setSelectedImage(img)}
            className={`relative aspect-square overflow-hidden rounded-xl transition-all duration-300 ${
              selectedImage === img
                ? "ring-2 ring-orange-500 ring-offset-2 scale-105"
                : "hover:scale-105 hover:shadow-lg"
            }`}
          >
            <Image
              src={img || "/placeholder.svg"}
              alt={`${product.title} view ${index + 1}`}
              fill
              className="object-cover"
            />
          </button>
        )
      )}
    </div>
  </div>
);

const extractHighlights = (longDescription: string | string[]): string[] => {
  if (!longDescription) return [];
  const descriptionText = Array.isArray(longDescription)
    ? longDescription.join("\n")
    : longDescription;
  if (descriptionText.includes("|")) {
    return descriptionText
      .split("|")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  if (descriptionText.includes("\n")) {
    return descriptionText
      .split("\n")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  if (
    descriptionText.includes("High Quality Print") &&
    descriptionText.includes("Customization Available")
  ) {
    return [
      "High Quality Print",
      "Customization Available",
      "Fast Processing",
      "Premium Materials",
    ];
  }
  return descriptionText
    .split(/[.!?]/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

const CheckoutSection = ({ product }: ProductDetailProps) => {
  const router = useRouter();
  const [isDesignDialogOpen, setIsDesignDialogOpen] = useState(false);

  const handleContinueToCheckout = () => {
    setIsDesignDialogOpen(true);
  };

  const handleDesignComplete = (designId: string) => {
    localStorage.setItem(
      "selectedProduct",
      JSON.stringify({
        id: product.id,
        title: product.title,
        price: product.price,
        image_url: product.image_url,
      })
    );

    router.push(`/checkout?product=${product.id}&designId=${designId}`);
  };

  return (
    <div className="space-y-4">
      <Button
        onClick={handleContinueToCheckout}
        size="lg"
        className="w-full h-14 text-lg font-semibold bg-gradient-to-r from-orange-600 to-amber-500 hover:from-orange-700 hover:to-amber-600 shadow-lg hover:shadow-xl transition-all duration-300"
      >
        <ShoppingCart className="mr-3 h-5 w-5" />
        Continue to Checkout
      </Button>

      <div className="flex gap-3">
        <Button variant="outline" size="lg" className="flex-1 h-12">
          <Heart className="mr-2 h-4 w-4" />
          Save
        </Button>
        <Button variant="outline" size="lg" className="flex-1 h-12">
          <Share2 className="mr-2 h-4 w-4" />
          Share
        </Button>
      </div>

      <DesignDialog
        isOpen={isDesignDialogOpen}
        onClose={() => setIsDesignDialogOpen(false)}
        productId={product.id.toString()}
        productName={product.title}
        onDesignComplete={handleDesignComplete}
      />
    </div>
  );
};

const ProductDetailModern = ({ product }: ProductDetailProps) => {
  const [selectedImage, setSelectedImage] = useState(product.image_url);

  const formattedTime = product.created_at
    ? new Date(product.created_at).toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      })
    : "New Arrival";

  const productHighlights = useMemo(
    () => extractHighlights(product.long_description),
    [product.long_description]
  );

  const formattedDescription = useMemo(() => {
    const descriptionText = Array.isArray(product.long_description)
      ? product.long_description.join("\n")
      : product.long_description;
    return descriptionText;
  }, [product.long_description]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
      {/* Navigation */}
      <div className="container mx-auto px-4 py-6">
        <Link
          href="/products"
          prefetch={true}
          className="flex items-center gap-1.5 px-3 py-1.5 text-sm bg-white text-orange-600 hover:bg-orange-50 border border-orange-600 rounded-md transition-colors duration-200 mb-4 w-fit"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Products</span>
        </Link>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Left Column - Images */}
          <div className="space-y-6">
            {/* Main Product Image */}
            <div className="relative aspect-square overflow-hidden rounded-2xl bg-white shadow-2xl">
              <Image
                src={selectedImage || "/placeholder.svg"}
                alt={product.title}
                className="object-cover transition-transform duration-500 hover:scale-105"
                fill
                priority
              />
              <Badge className="absolute top-4 left-4 bg-orange-600 hover:bg-orange-700 text-white shadow-md">
                {formattedTime}
              </Badge>
            </div>

            {/* Thumbnail Gallery */}
            <ProductGalleryThumbnails
              product={product}
              selectedImage={selectedImage}
              setSelectedImage={setSelectedImage}
            />
          </div>

          {/* Right Column - Product Info */}
          <div className="space-y-8">
            {/* Product Header */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm text-slate-600">
                <span className="font-medium">By</span>
                <Badge className="font-semibold">PRINTCLOUD DESIGN</Badge>
              </div>

              <h1 className="text-4xl lg:text-5xl font-bold text-slate-900 leading-tight">
                {product.title}
              </h1>

              <p className="text-xl text-slate-600 leading-relaxed">
                {product.short_description}
              </p>

              {/* Rating */}
              <div className="flex items-center gap-2">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < 4
                          ? "fill-yellow-400 text-yellow-400"
                          : "text-slate-300"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm text-neutral-600">
                  (4.0) • 127 reviews
                </span>
              </div>
            </div>

            <Separator />

            {/* Price */}
            <div className="space-y-2">
              <div className="text-4xl font-bold text-orange-700">
                $
                {typeof product.price === "string"
                  ? Number.parseFloat(product.price).toFixed(2)
                  : product.price.toFixed(2)}{" "}
                <span className="text-lg font-normal text-neutral-600">
                  CAD
                </span>
              </div>
              <p className="text-sm text-amber-600 font-medium">
                ✓ Free shipping on orders over $50
              </p>
            </div>

            <Separator />

            {/* Product Highlights */}
            {productHighlights.length > 0 && (
              <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-amber-50">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-4 text-orange-800">
                    What makes this special
                  </h3>
                  <div className="grid gap-3">
                    {productHighlights.map((highlight, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="bg-orange-500 rounded-full p-1 mt-0.5">
                          <Check className="h-3 w-3 text-white" />
                        </div>
                        <span className="text-orange-900 font-medium">
                          {highlight}
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Checkout Section */}
            <CheckoutSection product={product} />

            <Separator />

            {/* Product Description */}
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-orange-800">
                Description
              </h3>
              <div className="prose prose-orange max-w-none">
                <p className="text-neutral-700 leading-relaxed whitespace-pre-line">
                  {formattedDescription}
                </p>
              </div>
            </div>

            {/* Additional Info */}
            <Card className="border-orange-100">
              <CardContent className="p-6">
                <div className="grid gap-4">
                  <div className="flex justify-between items-center">
                    <span className="text-neutral-600">Processing Time</span>
                    <span className="font-semibold">2-3 business days</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="text-neutral-600">Shipping</span>
                    <span className="font-semibold">5-7 business days</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between items-center">
                    <span className="text-neutral-600">Return Policy</span>
                    <span className="font-semibold">30 days</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailModern;
