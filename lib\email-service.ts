import nodemailer, { SentMessageInfo } from 'nodemailer';

  const transporter = nodemailer.createTransport({
    host: 'smtp0001.neo.space', // Namecheap's SMTP server
    port: 587,
    secure: false, // true for 465, false for other ports like 587
    auth: {
      user: process.env.EMAIL_USER, // Your Namecheap email
      pass: process.env.EMAIL_PASSWORD, // Your Namecheap email password
    },
  });

export async function sendEmail({
  to,
  subject,
  html,
  from = process.env.EMAIL_USER
}: {
  to: string;
  subject: string;
  html: string;
  from?: string;
}): Promise<{ success: boolean; data?: SentMessageInfo; error?: Error | unknown }> {
  try {
    // Validate email parameters
    if (!to || !subject || !html) {
      throw new Error('Missing required email parameters');
    }

    if (!process.env.EMAIL_USER || !process.env.EMAIL_PASSWORD) {
      console.error('Email configuration missing - EMAIL_USER or EMAIL_PASSWORD not set');
      return { success: false, error: new Error('Email service not configured') };
    }

    // Log the size of the email content
    console.log(`Sending email to: ${to}, Subject: ${subject}, Size: ${html.length} characters`);

    // Gmail clips messages larger than ~102KB
    if (html.length > 100000) {
      console.warn('Warning: Email content exceeds 100KB and may be clipped by Gmail');
    }

    // Add timeout to email sending
    const emailPromise = transporter.sendMail({
      from,
      to,
      subject,
      html
    });

    // Set a timeout for email sending (30 seconds)
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Email sending timeout')), 30000);
    });

    const info = await Promise.race([emailPromise, timeoutPromise]) as SentMessageInfo;

    console.log('Email sent successfully:', info.messageId);
    return { success: true, data: info };
  } catch (error) {
    console.error('Error sending email:', error);

    // Don't expose sensitive email configuration errors
    const safeError = error instanceof Error
      ? new Error('Failed to send email')
      : new Error('Unknown email error');

    return { success: false, error: safeError };
  }
}
