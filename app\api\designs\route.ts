import { auth } from '@/lib/auth'
import pool from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST(request: Request) {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { productId, designData } = await request.json()

    if (!productId || !designData) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Save the design to the database
    const result = await pool.query(
      `INSERT INTO designs (user_id, product_id, design_data, created_at)
       VALUES ($1, $2, $3, NOW())
       RETURNING id`,
      [session.user.id, productId, designData]
    )

    return NextResponse.json({
      success: true,
      designId: result.rows[0].id
    })
  } catch (error) {
    console.error('Error saving design:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 