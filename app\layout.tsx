import type React from "react"
import type { Metadata } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/sonner"
import Header from "@/components/Header"
import Footer from "@/components/Footer"
import { AuthProvider } from "@/components/auth-provider"

const inter = Inter({ subsets: ["latin"] })

const businessName = "GServe Tech - Professional Printing Services";
const businessUrl = "https://print.gservetech.com"; // <<< UPDATED HERE
const businessDescription = `Fast, reliable printing services for all your business needs in Toronto, ON. Contact us at (************* or <EMAIL>. Open 24/7. Visit ${businessUrl}.`;
const googleVerification = "EP8Lp_bcqEQNfuZdu3qb0R7-DPdaMsThEr3trCoupBM";
const phoneNumber = "(*************";
const emailAddress = "<EMAIL>";
const city = "Toronto";
const province = "ON";
const country = "CA"; // Canada

export const metadata: Metadata = {
  title: `Print - ${businessName}`,
  description: businessDescription,
  verification: {
    google: googleVerification,
  },
  alternates: {
    canonical: businessUrl, // <<< USES businessUrl
  },
  openGraph: {
    title: `Print - ${businessName}`,
    description: businessDescription,
    url: businessUrl, // <<< USES businessUrl
    siteName: businessName,
    type: 'website',
    images: [
      // {
      //   url: `${businessUrl}/og-image.png`, // <<< USES businessUrl
      //   width: 1200,
      //   height: 630,
      //   alt: businessName,
      // },
    ],
    locale: 'en_CA',
  },
  twitter: {
    card: 'summary_large_image',
    title: `Print - ${businessName}`,
    description: businessDescription,
    // site: '@yourtwitterhandle', // Optional: Add your Twitter handle
    // creator: '@creatorhandle', // Optional: Add creator Twitter handle
    // images: [`${businessUrl}/twitter-image.png`], // <<< USES businessUrl
  },
  other: {
    'application-name': businessName,
    'msapplication-TileColor': '#ffffff',
    'theme-color': '#ffffff',
    'contact': emailAddress,
    'telephone': phoneNumber,
  },
};

const structuredData = {
  "@context": "https://schema.org",
  "@type": "ProfessionalService",
  "name": businessName,
  "description": businessDescription,
  "url": businessUrl, // <<< USES businessUrl
  "telephone": phoneNumber,
  "email": emailAddress,
  "address": {
    "@type": "PostalAddress",
    "addressLocality": city,
    "addressRegion": province,
    "addressCountry": country
  },
  "areaServed": {
    "@type": "AdministrativeArea", // Changed to AdministrativeArea for simplicity if radius is unknown
    "name": "Toronto"
    // Or, if you know the service radius for GeoCircle:
    // "@type": "GeoCircle",
    // "geoMidpoint": {
    //   "@type": "GeoCoordinates",
    //   "latitude": "43.651070", // General latitude for Toronto
    //   "longitude": "-79.347015" // General longitude for Toronto
    // },
    // "geoRadius": "50000" // Example: 50km radius
  },
  // "image": `${businessUrl}/logo.png`, // <<< USES businessUrl
  "openingHoursSpecification": [
    {
      "@type": "OpeningHoursSpecification",
      "dayOfWeek": [
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
        "Sunday"
      ],
      "opens": "00:00",
      "closes": "23:59"
    }
  ]
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
        />
      </head>
      <body className={inter.className}>
        <AuthProvider>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
            <div className="flex min-h-screen flex-col">
              <Header />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
            <Toaster />
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  )
}