"server only"

import Together from "together-ai"

const together = new Together({
  apiKey: process.env.TOGETHER_API_KEY
})

export type TextGenerationPrompt = {
  productType: string
  style: string
  tone: string
  keywords?: string[]
  length?: "short" | "medium" | "long"
}

export async function generateProductText({
  productType,
  style,
  tone,
  keywords = [],
  length = "medium"
}: TextGenerationPrompt): Promise<string> {
  const prompt = `Generate ${length} marketing text for a ${productType} with the following characteristics:
Style: ${style}
Tone: ${tone}
${keywords.length > 0 ? `Keywords to include: ${keywords.join(", ")}` : ""}

The text should be engaging, professional, and suitable for printing on the product.`

  try {
    const response = await together.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "You are a professional copywriter specializing in creating engaging marketing text for print products."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
      temperature: 0.7,
      max_tokens: length === "short" ? 100 : length === "medium" ? 200 : 300,
      stream: false
    })

    return response.choices[0]?.message?.content || "Failed to generate text"
  } catch (error) {
    console.error("Error generating text:", error)
    throw new Error("Failed to generate text. Please try again.")
  }
}

export async function generateStreamingText({
  productType,
  style,
  tone,
  keywords = [],
  length = "medium"
}: TextGenerationPrompt): Promise<ReadableStream> {
  const prompt = `Generate ${length} marketing text for a ${productType} with the following characteristics:
Style: ${style}
Tone: ${tone}
${keywords.length > 0 ? `Keywords to include: ${keywords.join(", ")}` : ""}

The text should be engaging, professional, and suitable for printing on the product.`

  try {
    const response = await together.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "You are a professional copywriter specializing in creating engaging marketing text for print products."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
      temperature: 0.7,
      max_tokens: length === "short" ? 100 : length === "medium" ? 200 : 300,
      stream: true
    })

    return new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of response) {
            const content = chunk.choices[0]?.delta?.content
            if (content) {
              controller.enqueue(content)
            }
          }
          controller.close()
        } catch (error) {
          controller.error(error)
        }
      }
    })
  } catch (error) {
    console.error("Error generating streaming text:", error)
    throw new Error("Failed to generate text. Please try again.")
  }
} 