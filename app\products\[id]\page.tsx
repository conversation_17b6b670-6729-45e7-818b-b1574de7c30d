import ProductDetail from './productDetail';
import { notFound } from "next/navigation";

type DBProduct = {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  subcategory_id?: number;
  created_at?: string;
};

async function getProduct(id: string): Promise<DBProduct | null> {
  try {
    // Use API route with caching instead of direct database query
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL;
    const res = await fetch(`${baseUrl}/api/products/${id}`, { 
      next: { revalidate: 1800 }, // Cache for 30 minutes
      cache: 'force-cache'
    });
    
    if (!res.ok) {
      if (res.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch product: ${res.status}`);
    }
    
    const product = await res.json();
    return product;
  } catch (error) {
    console.error("Error fetching product:", error);
    return null;
  }
}

export async function generateMetadata({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }

  const title = product.title;
  const description = product.short_description;

  return {
    title,
    description,
  };
}

export default async function ProductPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const product = await getProduct(id);

  if (!product) {
    notFound();
  }

  return (
    <div className="bg-white w-full py-8">
      <ProductDetail product={product} />
    </div>
  );
}

