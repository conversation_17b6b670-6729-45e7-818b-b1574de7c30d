"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, Wand2 } from "lucide-react"
import { toast } from "sonner"

interface AITextGeneratorProps {
  onTextGenerated: (text: string) => void
  productType: string
}

export function AITextGenerator({ onTextGenerated, productType }: AITextGeneratorProps) {
  const [style, setStyle] = useState("")
  const [tone, setTone] = useState("")
  const [keywords, setKeywords] = useState("")
  const [length, setLength] = useState<"short" | "medium" | "long">("medium")
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedText, setGeneratedText] = useState("")

  const handleGenerate = async () => {
    if (!style || !tone) {
      toast.error("Please fill in all required fields")
      return
    }

    setIsGenerating(true)
    setGeneratedText("")

    try {
      const response = await fetch("/api/generate-text", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          productType,
          style,
          tone,
          keywords: keywords.split(",").map(k => k.trim()).filter(Boolean),
          length,
          stream: true
        })
      })

      if (!response.ok) throw new Error("Failed to generate text")

      const reader = response.body?.getReader()
      if (!reader) throw new Error("No reader available")

      let fullText = ""
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const text = new TextDecoder().decode(value)
        fullText += text
        setGeneratedText(fullText)
      }

      onTextGenerated(fullText)
      toast.success("Text generated successfully")
    } catch (error) {
      console.error("Error generating text:", error)
      toast.error("Failed to generate text. Please try again.")
    } finally {
      setIsGenerating(false)
    }
  }

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-white">
      <div className="flex items-center gap-2 mb-4">
        <Wand2 className="w-5 h-5 text-blue-600" />
        <h3 className="font-semibold">AI Text Generator</h3>
      </div>

      <div className="grid gap-4">
        <div className="grid gap-2">
          <Label htmlFor="style">Style</Label>
          <Input
            id="style"
            placeholder="e.g., Modern, Classic, Minimalist"
            value={style}
            onChange={(e) => setStyle(e.target.value)}
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="tone">Tone</Label>
          <Input
            id="tone"
            placeholder="e.g., Professional, Friendly, Formal"
            value={tone}
            onChange={(e) => setTone(e.target.value)}
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="keywords">Keywords (comma-separated)</Label>
          <Input
            id="keywords"
            placeholder="e.g., quality, premium, innovative"
            value={keywords}
            onChange={(e) => setKeywords(e.target.value)}
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="length">Text Length</Label>
          <select
            id="length"
            value={length}
            onChange={(e) => setLength(e.target.value as "short" | "medium" | "long")}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <option value="short">Short</option>
            <option value="medium">Medium</option>
            <option value="long">Long</option>
          </select>
        </div>

        <Button
          onClick={handleGenerate}
          disabled={isGenerating || !style || !tone}
          className="w-full"
        >
          {isGenerating ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Generating...
            </>
          ) : (
            "Generate Text"
          )}
        </Button>

        {generatedText && (
          <div className="mt-4">
            <Label>Generated Text</Label>
            <Textarea
              value={generatedText}
              readOnly
              className="mt-2 min-h-[100px]"
            />
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => {
                navigator.clipboard.writeText(generatedText)
                toast.success("Text copied to clipboard")
              }}
            >
              Copy to Clipboard
            </Button>
          </div>
        )}
      </div>
    </div>
  )
} 