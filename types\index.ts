// Product Types
export interface Product {
  id: number;
  title: string | string[];
  shortDesc: string | string[];
  longDesc?: string | string[];
  price: number | string | string[];
  image: string;
  galleryImages?: string[];
}

// Cart Types
export interface CartItem extends Product {
  quantity: number;
}

// Order Types
export interface Order {
  id: string;
  items: CartItem[];
  date: string;
}

// Client Types
export interface Client {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
} 