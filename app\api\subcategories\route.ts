import { getPool } from "@/lib/db";
import { NextResponse } from "next/server";
import NodeCache from "node-cache";

// Load TTL from .env
const TTL = parseInt(process.env.SUBCATEGORY_CACHE_TTL || "86400", 10);

// Initialize cache (global to avoid re-init per request)
const subcategoryCache = new NodeCache();

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const categoryId = searchParams.get("categoryId");

  if (!categoryId) {
    return NextResponse.json({ subcategories: [] });
  }

  const cacheKey = `subcategories-${categoryId}`;
  const cachedData = subcategoryCache.get(cacheKey);
  if (cachedData) {
    return NextResponse.json({ subcategories: cachedData });
  }

  const pool = getPool();
  try {
    const result = await pool.query(
      "SELECT id, name FROM subcategory WHERE category_id = $1 ORDER BY name",
      [Number(categoryId)]
    );

    const subcategories = result.rows;
    subcategoryCache.set(cacheKey, subcategories, TTL);
    return NextResponse.json({ subcategories });
  } catch (error) {
    console.error("Failed to fetch subcategories:", error);
    return NextResponse.json({ error: "Failed to fetch subcategories" }, { status: 500 });
  }
}
