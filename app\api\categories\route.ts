import { getPool } from "@/lib/db";
import { NextResponse } from "next/server";
import NodeCache from "node-cache";

// Cache TTL: 1 day (in seconds)
const TTL = 86400;
const categoryCache = new NodeCache();

export async function GET() {
  const cacheKey = "categories";
  const cachedData = categoryCache.get(cacheKey);

  if (cachedData) {
    return NextResponse.json({ categories: cachedData });
  }

  const pool = getPool();
  try {
    const result = await pool.query("SELECT id, name FROM category ORDER BY name");
    categoryCache.set(cacheKey, result.rows, TTL);
    return NextResponse.json({ categories: result.rows });
  } catch (error) {
    console.error("Failed to fetch categories:", error);
    return NextResponse.json({ error: "Failed to fetch categories" }, { status: 500 });
  }
}