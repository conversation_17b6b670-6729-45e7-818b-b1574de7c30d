"use client";

import { ProductsGridSkeleton } from "@/components/ProductSkeleton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { revalidateProducts } from "@/lib/actions";
import { fetchProducts as cachedFetchProducts } from "@/lib/cached-fetch";
import {
  Eye,
  Filter,
  Pencil,
  Plus,
  RefreshCw,
  Search,
  SlidersHorizontal,
  Trash2,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface Product {
  id: string;
  title: string[];
  short_description: string[];
  long_description: string[];
  price: number;
  promotional_price_percent?: number;
  sales_price_percent?: number;
  image_url: string;
  gallery_urls?: string[];
  created_at?: string;
  category_id?: string;
  category_name?: string;
  subcategory_id?: string;
  is_featured?: boolean;
}

interface Category {
  id: string;
  name: string;
}

interface ManageProductsClientProps {
  initialProducts?: Product[];
  initialCategories?: Category[];
}

export function ManageProductsClient({ initialProducts = [], initialCategories = [] }: ManageProductsClientProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [loading, setLoading] = useState(initialProducts.length === 0);
  const [searchQuery, setSearchQuery] = useState("");

  const router = useRouter();
  const searchParams = useSearchParams();

  const selectedCategory = searchParams.get("category") || "";
  const sortBy = searchParams.get("sort") || "newest";

  useEffect(() => {
    // Only fetch if we don't have initial data or if filters changed
    if (initialProducts.length === 0 || initialCategories.length === 0) {
      // Fetch categories
      const fetchCategories = async () => {
        try {
          const res = await fetch("/api/categories", {
            cache: 'force-cache',
            next: { revalidate: 3600 } // Cache categories for 1 hour
          });
          if (res.ok) {
            const data = await res.json();
            setCategories(data.categories || []);
          }
        } catch (error) {
          console.error("Error fetching categories:", error);
        }
      };
      
      if (initialCategories.length === 0) {
        fetchCategories();
      }
      
      if (initialProducts.length === 0) {
        fetchProducts(selectedCategory, sortBy);
      }
    } else {
      // We have initial data, just update loading state
      setLoading(false);
    }
  }, [selectedCategory, sortBy, initialProducts.length, initialCategories.length]);

  const fetchProducts = async (categoryId?: string | null, sort?: string | null) => {
    setLoading(true);
    try {
      // Use the cached fetch function
      const products = await cachedFetchProducts(categoryId || undefined, sort || undefined);
      setProducts(products);
    } catch (error) {
      console.error("Error fetching products:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (categoryId) {
      params.set("category", categoryId);
    } else {
      params.delete("category");
    }
    router.push(`/admin/manage-products${params.toString() ? `?${params.toString()}` : ""}`);
  };

  const handleSortChange = (value: string) => {
    const params = new URLSearchParams(searchParams.toString());
    if (value && value !== "newest") {
      params.set("sort", value);
    } else {
      params.delete("sort");
    }
    router.push(`/admin/manage-products${params.toString() ? `?${params.toString()}` : ""}`);
  };

  const handleEditProduct = (product: Product) => {
    // TODO: Implement edit functionality
    console.log("Edit product:", product);
  };

  const handleDeleteProduct = (product: Product) => {
    // TODO: Implement delete functionality
    console.log("Delete product:", product);
  };

  // Add refresh function
  const refreshData = async () => {
    try {
      setLoading(true);
      
      // Revalidate products data
      await revalidateProducts();
      
      // Fetch fresh data
      await fetchProducts(selectedCategory, sortBy);
      
    } catch (error) {
      console.error("Error refreshing products:", error);
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = products.filter((product) => {
    if (!searchQuery) return true;
    const title = product.title[0]?.toLowerCase() || "";
    const description = product.short_description[0]?.toLowerCase() || "";
    const query = searchQuery.toLowerCase();
    return title.includes(query) || description.includes(query);
  });

  // Rest of the component implementation will be added in the next edit...
  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="bg-white rounded-xl shadow-sm border p-6 mb-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              Product Management
            </h1>
            <p className="text-gray-500 mt-1">Manage your product inventory</p>
          </div>
          <div className="flex gap-2">
            <Button onClick={refreshData} variant="outline" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
              Refresh
            </Button>
            <Button asChild className="bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700">
              <Link prefetch={true} href="/admin/add-product">
                <Plus className="h-4 w-4 mr-2" />
                Add New Product
              </Link>
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search products..."
              className="pl-10 bg-gray-50 border-gray-200"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {/* Filter and sort controls will be added in the next edit... */}
        </div>

        {/* Filter and sort controls */}
        <div className="flex gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <span className="hidden sm:inline">Filter</span>
                {selectedCategory && (
                  <Badge className="ml-1 bg-violet-100 text-violet-800 hover:bg-violet-200">1</Badge>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56">
              <div className="space-y-2">
                <h4 className="font-medium">Filter by Category</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="all"
                      name="category"
                      value=""
                      checked={selectedCategory === ""}
                      onChange={(e) => handleCategoryChange(e.target.value)}
                    />
                    <label htmlFor="all">All Categories</label>
                  </div>
                  {categories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <input
                        type="radio"
                        id={String(category.id)}
                        name="category"
                        value={String(category.id)}
                        checked={selectedCategory === String(category.id)}
                        onChange={(e) => handleCategoryChange(e.target.value)}
                      />
                      <label htmlFor={String(category.id)}>{category.name}</label>
                    </div>
                  ))}
                </div>
              </div>
            </PopoverContent>
          </Popover>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4" />
                <span className="hidden sm:inline">Sort</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56">
              <div className="space-y-2">
                <h4 className="font-medium">Sort by</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="newest"
                      name="sort"
                      value="newest"
                      checked={sortBy === "newest"}
                      onChange={(e) => handleSortChange(e.target.value)}
                    />
                    <label htmlFor="newest">Newest</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="price_asc"
                      name="sort"
                      value="price_asc"
                      checked={sortBy === "price_asc"}
                      onChange={(e) => handleSortChange(e.target.value)}
                    />
                    <label htmlFor="price_asc">Price: Low to High</label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="price_desc"
                      name="sort"
                      value="price_desc"
                      checked={sortBy === "price_desc"}
                      onChange={(e) => handleSortChange(e.target.value)}
                    />
                    <label htmlFor="price_desc">Price: High to Low</label>
                  </div>
                </div>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Products grid */}
      {loading ? (
        <ProductsGridSkeleton />
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredProducts.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Search className="h-10 w-10 text-gray-400" />
              </div>
              <h3 className="text-xl font-medium text-gray-600">No products found</h3>
              <p className="mt-2 text-gray-500">Try adjusting your filters or search query.</p>
            </div>
          ) : (
            filteredProducts.map((product: Product) => (
              <Card
                key={product.id}
                className="group overflow-hidden border-gray-200 h-full flex flex-col transition-all duration-200 hover:shadow-md hover:border-gray-300 hover:-translate-y-1"
              >
                <div className="relative aspect-square w-full overflow-hidden bg-gray-100">
                  <Link href={`/products/${product.id}`} prefetch={true}>
                    <Image
                      src={
                        product.image_url && product.image_url.trim() !== ""
                          ? product.image_url
                          : "/placeholder.svg?height=400&width=400"
                      }
                      alt={Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      className="object-cover cursor-pointer"
                    />
                  </Link>
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 pointer-events-none"></div>

                  {product.promotional_price_percent && (
                    <div className="absolute top-3 left-3">
                      <Badge className="bg-red-500 hover:bg-red-600">
                        {product.promotional_price_percent}% OFF
                      </Badge>
                    </div>
                  )}
                </div>
                <CardContent className="p-5 flex flex-col flex-grow">
                  {product.category_name && (
                    <Badge className="mb-2 self-start text-xs font-normal text-gray-600">
                      {product.category_name}
                    </Badge>
                  )}
                  <h3 className="font-semibold text-lg mb-1 line-clamp-1 group-hover:text-violet-700 transition-colors">
                    {Array.isArray(product.title) && product.title.length > 0 ? product.title[0] : "Product"}
                  </h3>
                  <p className="text-gray-500 text-sm mb-2 line-clamp-2">
                    {Array.isArray(product.short_description) && product.short_description.length > 0
                      ? product.short_description[0]
                      : ""}
                  </p>
                </CardContent>
                <CardFooter className="px-5 pb-5 pt-0 flex items-center justify-between mt-auto">
                  <div>
                    <p className="font-bold text-lg text-violet-700">${Number(product.price).toFixed(2)}</p>
                    {product.sales_price_percent && (
                      <p className="text-xs text-gray-500 line-through">
                        ${Number(product.price / (1 - product.sales_price_percent / 100)).toFixed(2)}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="h-9 border-gray-200 hover:bg-violet-50 hover:text-violet-700 hover:border-violet-200"
                      onClick={() => handleEditProduct(product)}
                      title="Edit"
                    >
                      <Pencil className="h-4 w-4" />
                    </Button>
                    <Button asChild className="h-9 bg-violet-600 hover:bg-violet-700 text-white" title="View">
                      <Link prefetch={true} href={`/products/${product.id}`}>
                        <Eye className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      className="h-9 bg-red-600 hover:bg-red-700 text-white"
                      onClick={() => handleDeleteProduct(product)}
                      title="Delete"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
      )}
    </div>
  );
}
