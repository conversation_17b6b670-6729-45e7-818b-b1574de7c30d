import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent } from "@/components/ui/card";

export function ProductSkeleton() {
  return (
    <Card className="overflow-hidden h-full">
      <div className="relative aspect-square w-full bg-gray-200 animate-pulse">
        <Skeleton className="h-full w-full" />
        {/* Express badge skeleton */}
        <div className="absolute top-2 right-2 w-16 h-6 rounded-md">
          <Skeleton className="h-full w-full" />
        </div>
      </div>
      <CardContent className="p-4">
        <Skeleton className="h-6 w-2/3 mb-2 animate-pulse" />
        <Skeleton className="h-4 w-full mb-2 animate-pulse" />
        <Skeleton className="h-4 w-3/4 mb-4 animate-pulse" />
        <div className="flex items-center justify-between mt-auto">
          <Skeleton className="h-5 w-24 animate-pulse" />
          <Skeleton className="h-9 w-28 rounded-md animate-pulse" />
        </div>
      </CardContent>
    </Card>
  );
}

export function ProductsGridSkeleton({ count = 8 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {Array(count).fill(0).map((_, index) => (
        <ProductSkeleton key={index} />
      ))}
    </div>
  );
}

// Add this to your global CSS (app/globals.css)
// @keyframes pulse-subtle {
//   0%, 100% { opacity: 1; }
//   50% { opacity: 0.7; }
// }
// .animate-pulse-subtle {
//   animation: pulse-subtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
// } 