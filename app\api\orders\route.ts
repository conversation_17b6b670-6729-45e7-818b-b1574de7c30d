import { auth } from "@/lib/auth"
import { getPool } from "@/lib/db"
import { NextResponse } from "next/server"

interface OrderItem {
  id: number;
  name: string;
  quantity: number;
  price: number;
  image: string;
  customDesignId: string;
}

export async function GET() {
  try {
    const session = await auth()

    if (!session?.user) {
      return new NextResponse("Unauthorized", { status: 401 })
    }

    const pool = getPool()
    const client = await pool.connect()

    try {
      await client.query("BEGIN")

      // Use a single query with JSON aggregation to avoid N+1 problem
      const ordersResult = await client.query(
        `SELECT
          o.id,
          o.order_number,
          o.status,
          o.total_amount,
          o.delivery_method,
          o.delivery_fee,
          o.promotional_discount,
          o.special_instructions,
          o.created_at,
          o.updated_at,
          da.street,
          da.city,
          da.province,
          da.postal_code,
          COALESCE(
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', oi.id,
                'name', oi.product_name,
                'quantity', oi.quantity,
                'price', oi.price_at_order,
                'image', COALESCE(p.image_url, '/placeholder.svg'),
                'customDesignId', 'custom-design'
              ) ORDER BY oi.id
            ) FILTER (WHERE oi.id IS NOT NULL),
            '[]'::json
          ) as items
         FROM orders o
         LEFT JOIN delivery_addresses da ON o.id = da.order_id
         LEFT JOIN order_items oi ON o.id = oi.order_id
         LEFT JOIN product p ON CAST(oi.product_id AS INTEGER) = p.id
         WHERE o.user_id = $1
         GROUP BY o.id, da.id
         ORDER BY o.created_at DESC`,
        [session.user.id]
      )

      const orders = ordersResult.rows.map(order => ({
        id: order.id,
        orderNumber: order.order_number,
        status: order.status,
        items: order.items,
        subtotal: order.items.reduce((sum: number, item: OrderItem) => sum + (item.price * item.quantity), 0),
        deliveryFee: order.delivery_fee,
        promotionalDiscount: order.promotional_discount,
        totalAmount: order.total_amount,
        deliveryMethod: order.delivery_method,
        deliveryAddress: order.street ? {
          street: order.street,
          city: order.city,
          province: order.province,
          postalCode: order.postal_code
        } : undefined,
        specialInstructions: order.special_instructions,
        createdAt: order.created_at,
        estimatedDeliveryTime: order.delivery_method === "DELIVERY"
          ? new Date(new Date(order.created_at).getTime() + 45 * 60000).toLocaleString()
          : undefined,
        customDesignImageUrl: order.custom_design_image_url || null
      }))

      await client.query("COMMIT")

      return NextResponse.json(orders)
    } catch (error) {
      await client.query("ROLLBACK")
      throw error
    } finally {
      client.release()
    }
  } catch (error) {
    console.error("Error fetching orders:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
} 