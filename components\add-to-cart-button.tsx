"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ShoppingCart, Check } from "lucide-react"
import { useCartStore, type CartItem } from "@/store/cart-store"
import { toast } from "sonner"

interface Product {
  id: string
  name: string
  price: number
  images: string[]
}

interface AddToCartButtonProps {
  product: Product
}

export default function AddToCartButton({ product }: AddToCartButtonProps) {
  const [isAdding, setIsAdding] = useState(false)
  const addItem = useCartStore((state) => state.addItem)

  const handleAddToCart = () => {
    setIsAdding(true)

    // Create cart item from product
    const cartItem: CartItem = {
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      image: product.images[0],
    }

    // Add to cart
    addItem(cartItem)

    // Show success toast
    toast("success", {
      description: `${product.name} has been added to your cart.`,
    })

    // Reset button state after a short delay
    setTimeout(() => {
      setIsAdding(false)
    }, 1500)
  }

  return (
    <Button onClick={handleAddToCart} className="bg-blue-600 hover:bg-blue-700 text-white" disabled={isAdding}>
      {isAdding ? (
        <>
          <Check className="mr-2 h-5 w-5" />
          Added to Cart
        </>
      ) : (
        <>
          <ShoppingCart className="mr-2 h-5 w-5" />
          Add to Cart
        </>
      )}
    </Button>
  )
}
