import { executeQuery } from './db-utils';

/**
 * Database performance monitoring utilities
 */

export interface QueryPerformanceStats {
  query: string;
  calls: number;
  totalTime: number;
  meanTime: number;
  rows: number;
}

export interface ConnectionStats {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  maxConnections: number;
}

export interface TableStats {
  tableName: string;
  rowCount: number;
  tableSize: string;
  indexSize: string;
  seqScan: number;
  seqTupRead: number;
  idxScan: number;
  idxTupFetch: number;
}

/**
 * Get slow queries from pg_stat_statements
 */
export async function getSlowQueries(limit: number = 10): Promise<QueryPerformanceStats[]> {
  try {
    const result = await executeQuery<QueryPerformanceStats>(`
      SELECT 
        query,
        calls,
        total_exec_time as totalTime,
        mean_exec_time as meanTime,
        rows
      FROM pg_stat_statements 
      WHERE query NOT LIKE '%pg_stat_statements%'
      ORDER BY mean_exec_time DESC 
      LIMIT $1
    `, [limit]);
    
    return result.rows;
  } catch (error) {
    console.warn('pg_stat_statements extension not available:', error);
    return [];
  }
}

/**
 * Get current connection statistics
 */
export async function getConnectionStats(): Promise<ConnectionStats> {
  const result = await executeQuery<{
    state: string;
    count: number;
    max_conn: number;
  }>(`
    SELECT 
      state,
      COUNT(*) as count,
      (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_conn
    FROM pg_stat_activity 
    WHERE datname = current_database()
    GROUP BY state, max_conn
  `);

  const stats = result.rows.reduce((acc, row) => {
    if (row.state === 'active') acc.activeConnections = row.count;
    if (row.state === 'idle') acc.idleConnections = row.count;
    acc.maxConnections = row.max_conn;
    return acc;
  }, {
    totalConnections: 0,
    activeConnections: 0,
    idleConnections: 0,
    maxConnections: 0
  });

  stats.totalConnections = stats.activeConnections + stats.idleConnections;
  return stats;
}

/**
 * Get table statistics for performance analysis
 */
export async function getTableStatistics(): Promise<TableStats[]> {
  const result = await executeQuery<TableStats>(`
    SELECT 
      schemaname || '.' || tablename as tableName,
      n_tup_ins + n_tup_upd + n_tup_del as rowCount,
      pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as tableSize,
      pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) as indexSize,
      seq_scan as seqScan,
      seq_tup_read as seqTupRead,
      idx_scan as idxScan,
      idx_tup_fetch as idxTupFetch
    FROM pg_stat_user_tables 
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
  `);

  return result.rows;
}

/**
 * Get index usage statistics
 */
export async function getIndexUsageStats(): Promise<Array<{
  tableName: string;
  indexName: string;
  indexScans: number;
  tupleReads: number;
  tuplesFetched: number;
}>> {
  interface IndexUsageRow {
    tableName: string;
    indexName: string;
    indexScans: number;
    tupleReads: number;
    tuplesFetched: number;
  }

  const result = await executeQuery<IndexUsageRow>(`
    SELECT
      schemaname || '.' || tablename as tableName,
      indexrelname as indexName,
      idx_scan as indexScans,
      idx_tup_read as tupleReads,
      idx_tup_fetch as tuplesFetched
    FROM pg_stat_user_indexes
    ORDER BY idx_scan DESC
  `);

  return result.rows;
}

/**
 * Get cache hit ratio
 */
export async function getCacheHitRatio(): Promise<{
  tableHitRatio: number;
  indexHitRatio: number;
}> {
  const result = await executeQuery<{
    table_hit_ratio: number;
    index_hit_ratio: number;
  }>(`
    SELECT 
      ROUND(
        100 * sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read) + 1), 2
      ) as table_hit_ratio,
      ROUND(
        100 * sum(idx_blks_hit) / (sum(idx_blks_hit) + sum(idx_blks_read) + 1), 2
      ) as index_hit_ratio
    FROM pg_statio_user_tables
  `);

  const row = result.rows[0];
  return {
    tableHitRatio: row?.table_hit_ratio || 0,
    indexHitRatio: row?.index_hit_ratio || 0
  };
}

/**
 * Get database size information
 */
export async function getDatabaseSize(): Promise<{
  databaseSize: string;
  tableSize: string;
  indexSize: string;
}> {
  const result = await executeQuery<{
    database_size: string;
    table_size: string;
    index_size: string;
  }>(`
    SELECT 
      pg_size_pretty(pg_database_size(current_database())) as database_size,
      pg_size_pretty(sum(pg_total_relation_size(schemaname||'.'||tablename))) as table_size,
      pg_size_pretty(sum(pg_indexes_size(schemaname||'.'||tablename))) as index_size
    FROM pg_stat_user_tables
  `);

  const row = result.rows[0];
  return {
    databaseSize: row?.database_size || '0 bytes',
    tableSize: row?.table_size || '0 bytes',
    indexSize: row?.index_size || '0 bytes'
  };
}

/**
 * Analyze query performance and suggest optimizations
 */
export async function analyzePerformance(): Promise<{
  slowQueries: QueryPerformanceStats[];
  connectionStats: ConnectionStats;
  tableStats: TableStats[];
  cacheHitRatio: { tableHitRatio: number; indexHitRatio: number };
  databaseSize: { databaseSize: string; tableSize: string; indexSize: string };
  recommendations: string[];
}> {
  const [slowQueries, connectionStats, tableStats, cacheHitRatio, databaseSize] = await Promise.all([
    getSlowQueries(5),
    getConnectionStats(),
    getTableStatistics(),
    getCacheHitRatio(),
    getDatabaseSize()
  ]);

  const recommendations: string[] = [];

  // Analyze and provide recommendations
  if (cacheHitRatio.tableHitRatio < 95) {
    recommendations.push('Consider increasing shared_buffers for better cache hit ratio');
  }

  if (connectionStats.activeConnections > connectionStats.maxConnections * 0.8) {
    recommendations.push('High connection usage detected, consider connection pooling');
  }

  const tablesWithHighSeqScans = tableStats.filter(t => t.seqScan > t.idxScan * 2);
  if (tablesWithHighSeqScans.length > 0) {
    recommendations.push(`Tables with high sequential scans: ${tablesWithHighSeqScans.map(t => t.tableName).join(', ')}. Consider adding indexes.`);
  }

  if (slowQueries.length > 0) {
    recommendations.push(`${slowQueries.length} slow queries detected. Review query optimization.`);
  }

  return {
    slowQueries,
    connectionStats,
    tableStats,
    cacheHitRatio,
    databaseSize,
    recommendations
  };
}

/**
 * Log performance metrics
 */
export async function logPerformanceMetrics(): Promise<void> {
  try {
    const analysis = await analyzePerformance();
    
    console.log('=== Database Performance Report ===');
    console.log(`Database Size: ${analysis.databaseSize.databaseSize}`);
    console.log(`Cache Hit Ratio - Tables: ${analysis.cacheHitRatio.tableHitRatio}%, Indexes: ${analysis.cacheHitRatio.indexHitRatio}%`);
    console.log(`Connections - Active: ${analysis.connectionStats.activeConnections}, Total: ${analysis.connectionStats.totalConnections}/${analysis.connectionStats.maxConnections}`);
    
    if (analysis.slowQueries.length > 0) {
      console.log('\nSlow Queries:');
      analysis.slowQueries.forEach((query, index) => {
        console.log(`${index + 1}. Mean time: ${query.meanTime.toFixed(2)}ms, Calls: ${query.calls}`);
        console.log(`   Query: ${query.query.substring(0, 100)}...`);
      });
    }

    if (analysis.recommendations.length > 0) {
      console.log('\nRecommendations:');
      analysis.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
    
    console.log('=== End Performance Report ===\n');
  } catch (error) {
    console.error('Error generating performance report:', error);
  }
}
