"use client";

import { useState, useMemo } from "react";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Star, ArrowLeft } from "lucide-react";
import DesignDialog from "@/components/DesignDialog";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

interface Product {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  created_at?: string;
}

console.log("minimal");

interface ProductDetailProps {
  product: Product;
}

const extractHighlights = (longDescription: string | string[]): string[] => {
  if (!longDescription) return [];
  const descriptionText = Array.isArray(longDescription)
    ? longDescription.join("\n")
    : longDescription;
  if (descriptionText.includes("|")) {
    return descriptionText
      .split("|")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  if (descriptionText.includes("\n")) {
    return descriptionText
      .split("\n")
      .map((item) => item.trim())
      .filter((item) => item.length > 0);
  }
  return descriptionText
    .split(/[.!?]/)
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

const ProductDetailMinimal = ({ product }: ProductDetailProps) => {
  const [selectedImage, setSelectedImage] = useState(product.image_url);
  const [isDesignDialogOpen, setIsDesignDialogOpen] = useState(false);
  const router = useRouter();

  const handleContinueToCheckout = () => {
    setIsDesignDialogOpen(true);
  };

  const handleDesignComplete = (designId: string) => {
    localStorage.setItem(
      "selectedProduct",
      JSON.stringify({
        id: product.id,
        title: product.title,
        price: product.price,
        image_url: product.image_url,
      })
    );
    router.push(`/checkout?product=${product.id}&designId=${designId}`);
  };

  const productHighlights = useMemo(
    () => extractHighlights(product.long_description),
    [product.long_description]
  );

  const formattedDescription = useMemo(() => {
    const descriptionText = Array.isArray(product.long_description)
      ? product.long_description.join("\n")
      : product.long_description;
    if (
      productHighlights.every(
        (highlight) => !descriptionText.includes(highlight)
      )
    ) {
      return descriptionText;
    }
    return descriptionText;
  }, [product.long_description, productHighlights]);

  return (
    <>
      <div className="bg-[#F4F0E6] text-[#4A4A4A] font-serif leading-relaxed min-h-screen">
        <div className="max-w-6xl mx-auto p-8">
          {/* Header */}
          <header className="mb-12">
            <div className="bg-[#2C2C2C] text-white px-6 py-3 inline-block text-sm font-sans uppercase tracking-widest mb-6">
              PROFESSIONAL PRINTING
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-[#C4941A] mb-4 leading-tight">
              {product.title}
            </h1>
            <p className="text-xl italic leading-relaxed max-w-2xl">
              {product.short_description}
            </p>
          </header>

          {/* Main Editorial Layout */}
          <div className="grid grid-cols-12 gap-8 mb-16">
            {/* Left Column - Product Image & Pricing */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-[#EDE7DB] p-6 rounded-lg mb-6">
                <div className="bg-[#C4941A] text-white px-4 py-2 text-sm font-bold uppercase tracking-wide mb-4 inline-block">
                  PREMIUM
                </div>
                <div className="relative w-full h-64 rounded-lg shadow-lg overflow-hidden mb-4">
                  <Image
                    src={selectedImage || "/placeholder.svg?height=300&width=400"}
                    alt={product.title}
                    fill
                    className="object-cover"
                    priority
                  />
                </div>
                {/* Gallery Thumbnails */}
                {product.gallery_urls && product.gallery_urls.length > 0 && (
                  <div className="grid grid-cols-4 gap-2 mt-2">
                    {[product.image_url, ...product.gallery_urls].map((img) => (
                      <button
                        key={img}
                        onClick={() => setSelectedImage(img)}
                        className={`relative aspect-square overflow-hidden rounded-lg border-2 ${
                          selectedImage === img
                            ? "border-[#C4941A]"
                            : "border-transparent"
                        }`}
                      >
                        <Image
                          src={img}
                          alt={`${product.title} gallery`}
                          fill
                          className="object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {/* Quick Pricing Box */}
              <div className="bg-[#C4941A] text-white p-6 rounded-lg">
                <h3 className="text-2xl font-bold mb-4">
                  Price: $
                  {typeof product.price === "string"
                    ? parseFloat(product.price).toFixed(2)
                    : product.price?.toFixed(2) || "29.99"}
                </h3>
                <div className="text-sm mb-2">
                  Free shipping on orders over $50
                </div>
                <div className="space-y-4">
                  <Button
                    onClick={handleContinueToCheckout}
                    size="lg"
                    className="w-full bg-gray-900 hover:bg-gray-800 text-white mt-6 transition-colors duration-200"
                  >
                    <ShoppingCart className="mr-2 h-5 w-5" />
                    Start Design
                  </Button>
                  
                  <Link
                      href="/products"
                      prefetch={true}
                      className="relative flex items-center justify-center w-full py-3 px-4 text-white font-medium rounded-lg border-2 border-gray hover:border-gray transition-all duration-200"
                    >
                      <ArrowLeft className="h-5 w-5 mr-2" />
                      Back to All Products
                    </Link>
                </div>
              </div>
            </div>

            {/* Right Column - Editorial Style Content */}
            <div className="col-span-12 lg:col-span-8">
              {/* Product Highlights */}
              {productHighlights.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-[#C4941A] mb-4">
                    Product Highlights
                  </h3>
                  <ul className="space-y-2 text-base">
                    {productHighlights.map((highlight, idx) => (
                      <li key={idx} className="flex items-start">
                        <span className="bg-[#C4941A]/10 text-[#C4941A] rounded-full p-1 mr-2 flex-shrink-0">
                          <Star className="h-4 w-4" />
                        </span>
                        <span>{highlight}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Main Article Content */}
              <div className="bg-white p-8 rounded-lg shadow-sm mb-8">
                <h2 className="text-2xl font-bold text-[#C4941A] mb-6">
                  Product Description
                </h2>
                <div className="leading-relaxed space-y-4 text-justify whitespace-pre-line">
                  {formattedDescription}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Design Dialog - rendered at the root level outside of any other containers */}
      <DesignDialog
        isOpen={isDesignDialogOpen}
        onClose={() => setIsDesignDialogOpen(false)}
        productId={product.id.toString()}
        productName={product.title}
        onDesignComplete={handleDesignComplete}
      />
    </>
  );
};

export default ProductDetailMinimal;
