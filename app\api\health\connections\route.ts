import { NextResponse } from 'next/server';
import { getConnectionHealth, getPoolStats, getDatabaseConnectionInfo } from '@/lib/connection-monitor';
import { isApplicationShuttingDown } from '@/lib/db';

export async function GET() {
  try {
    // Check if application is shutting down
    if (isApplicationShuttingDown()) {
      return NextResponse.json({
        status: 'shutting_down',
        message: 'Application is shutting down'
      }, { status: 503 });
    }

    // Get comprehensive connection health information
    const [health, poolStats, dbInfo] = await Promise.all([
      getConnectionHealth(),
      getPoolStats(),
      getDatabaseConnectionInfo()
    ]);

    const response = {
      timestamp: new Date().toISOString(),
      status: health.status,
      pool: {
        total: poolStats.totalCount,
        idle: poolStats.idleCount,
        waiting: poolStats.waitingCount,
        max: poolStats.maxConnections,
        utilization: poolStats.utilizationPercent
      },
      database: {
        active: dbInfo.activeConnections,
        idle: dbInfo.idleConnections,
        total: dbInfo.totalConnections,
        max: dbInfo.maxConnections,
        utilization: Math.round((dbInfo.totalConnections / dbInfo.maxConnections) * 100)
      },
      issues: health.issues,
      recommendations: health.recommendations,
      connectionsByState: dbInfo.connectionsByState
    };

    // Set appropriate status code based on health
    const statusCode = health.status === 'critical' ? 503 : 
                      health.status === 'warning' ? 200 : 200;

    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    console.error('Error checking connection health:', error);
    return NextResponse.json({
      status: 'error',
      message: 'Unable to check connection health',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
