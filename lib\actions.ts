'use server'

import { revalidateTag } from 'next/cache'

// Function to revalidate dashboard data
export async function revalidateDashboard() {
  // Revalidate all dashboard-related data
  revalidateTag('dashboard-data')
}

// Function to revalidate specific data types
export async function revalidateOrders() {
  revalidateTag('orders-data')
}

export async function revalidateProfile() {
  revalidateTag('profile-data')
}

// Function to revalidate products data
export async function revalidateProducts() {
  revalidateTag('products-data')
} 