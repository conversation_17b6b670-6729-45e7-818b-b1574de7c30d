"use client";

import { useEffect, useState } from "react";
import { ProductDetailSkeleton } from "@/components/ProductDetailSkeleton";

interface Product {
  id: number;
  title: string;
  short_description: string;
  long_description: string | string[];
  price: string | number;
  image_url: string;
  gallery_urls: string[];
  created_at?: string;
}

interface ProductDetailProps {
  product: Product;
}

// Dynamic variant imports
const VARIANT_COMPONENTS = [
  () => import("./ProductDetailClassic"), // index 0: classic/original style
  () => import("./ProductDetailModern"),  // index 1: modern style
  () => import("./ProductDetailMinimal"),
  () => import("./ProductDetailModern2"),
  () => import("./ProductDetailPremium"),
];

const ProductDetail = ({ product }: ProductDetailProps) => {
  
  const [DynamicVariant, setDynamicVariant] = useState<React.ComponentType<ProductDetailProps>| null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const random = Math.floor(Math.random() * VARIANT_COMPONENTS.length);
    
    VARIANT_COMPONENTS[random]()
      .then((mod) => {
        setDynamicVariant(() => mod.default);
        // Add a small delay to ensure smoother transition from skeleton to actual content
        setTimeout(() => setLoading(false), 800);
      });
  }, []);

  if (loading) return <ProductDetailSkeleton />;
  if (!DynamicVariant) return null;

  return <DynamicVariant product={product} />;
};

export default ProductDetail;
