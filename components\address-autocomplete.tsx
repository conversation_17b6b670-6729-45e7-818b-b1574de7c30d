'use client'

import { Input } from '@/components/ui/input'
import { MapboxFeature } from '@/types/address.types'
import { canadianProvinces } from '@/utils/states'
import React, { ChangeEvent, useEffect, useRef, useState } from 'react'

interface AddressAutocompleteProps {
  onAddressSelect: (address: {
    street: string
    city: string
    province: string
    postalCode: string
  }) => void
}

const AddressAutocomplete = ({ onAddressSelect }: AddressAutocompleteProps) => {
  const [suggestions, setSuggestions] = useState<MapboxFeature[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const suggestionComponentRef = useRef<HTMLDivElement | null>(null)
  const debounceTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionComponentRef.current &&
        !suggestionComponentRef.current.contains(event.target as Node)
      ) {
        setSuggestions([])
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Map province codes to full names
  const getProvinceCode = (provinceName: string): string => {
    const province = canadianProvinces.find(
      p => p.name.toLowerCase() === provinceName.toLowerCase() ||
          p.code.toLowerCase() === provinceName.toLowerCase()
    )
    return province?.code || provinceName
  }

  const handleInputChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)
    setError(null)

    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current)
    }

    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        if (!value.trim()) {
          setSuggestions([])
          return
        }

        setIsLoading(true)
        const response = await fetch(
          `/api/getPlaces?query=${encodeURIComponent(value)}`
        )

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || `API error: ${response.status}`)
        }

        const data = await response.json()

        if (data && data.data && Array.isArray(data.data.features)) {
          setSuggestions(data.data.features)
        } else {
          console.warn('Invalid response format from API:', data)
          setSuggestions([])
        }
      } catch (error) {
        console.error('Error fetching address suggestions:', error)
        setError(error instanceof Error ? error.message : 'Failed to fetch suggestions')
        setSuggestions([])
      } finally {
        setIsLoading(false)
      }
    }, 300)
  }

  const handleSuggestionClick = (suggestion: MapboxFeature) => {
    const streetAndNumber = suggestion.place_name.split(',')[0]

    const addressComponents = {
      postcode: '',
      place: '',
      region: '',
    }

    if (suggestion.context && Array.isArray(suggestion.context)) {
      suggestion.context.forEach((context) => {
        const idParts = context.id.split('.')
        if (idParts.length > 0) {
          const type = idParts[0]
          if (type === 'postcode') {
            addressComponents.postcode = context.text
          } else if (type === 'place') {
            addressComponents.place = context.text
          } else if (type === 'region') {
            // Convert province name to code
            addressComponents.region = getProvinceCode(context.text)
          }
        }
      })
    }

    if (!addressComponents.place) {
      const placeNameParts = suggestion.place_name.split(',').map(part => part.trim())
      
      for (let i = 1; i < placeNameParts.length; i++) {
        const part = placeNameParts[i]
        if (part === addressComponents.postcode || part === addressComponents.region) {
          continue
        }
        addressComponents.place = part
        break
      }
    }

    if (!addressComponents.place && suggestion.place_name.includes(',')) {
      const parts = suggestion.place_name.split(',').map((part) => part.trim())
      const lastPart = parts[parts.length - 1]
      if (lastPart !== addressComponents.postcode && lastPart !== addressComponents.region) {
        addressComponents.place = lastPart
      }
    }

    if (!addressComponents.place) {
      const parts = suggestion.place_name.split(',').map(part => part.trim())
      for (const part of parts) {
        if (part !== streetAndNumber && part !== addressComponents.postcode && part !== addressComponents.region) {
          addressComponents.place = part
          break
        }
      }
    }

    if (!addressComponents.place) {
      addressComponents.place = 'Toronto'
    }

    // If we still don't have a province, try to extract it from place_name
    if (!addressComponents.region) {
      const parts = suggestion.place_name.split(',').map(part => part.trim())
      for (const part of parts) {
        const provinceCode = getProvinceCode(part)
        if (provinceCode !== part) { // If the part was successfully converted to a province code
          addressComponents.region = provinceCode
          break
        }
      }
    }

    onAddressSelect({
      street: streetAndNumber,
      city: addressComponents.place,
      province: addressComponents.region,
      postalCode: addressComponents.postcode,
    })

    setInputValue(streetAndNumber)
    setSuggestions([])
    setError(null)
  }

  return (
    <div ref={suggestionComponentRef} className="relative">
      <Input
        type="search"
        placeholder="Enter your street address"
        value={inputValue}
        onChange={handleInputChange}
        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
      />

      {isLoading && (
        <div className="absolute right-3 top-2.5">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
        </div>
      )}

      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}

      {suggestions.length > 0 && (
        <ul className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <li
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="px-4 py-2 cursor-pointer hover:bg-gray-50 text-sm"
            >
              {suggestion.place_name}
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}

export default AddressAutocomplete 