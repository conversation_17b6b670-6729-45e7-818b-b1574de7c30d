/**
 * Circuit breaker pattern for database connections
 * Helps prevent cascading failures when database connections are exhausted
 */

interface CircuitBreakerState {
  failures: number;
  lastFailureTime: number;
  state: 'CLOSED' | 'OPEN' | 'HALF_OPEN';
}

class DatabaseCircuitBreaker {
  private state: CircuitBreakerState = {
    failures: 0,
    lastFailureTime: 0,
    state: 'CLOSED'
  };

  private readonly failureThreshold = 3; // Open circuit after 3 failures
  private readonly recoveryTimeout = 30000; // 30 seconds before trying again
  private readonly resetTimeout = 60000; // 1 minute to reset failure count

  /**
   * Check if we should allow database operations
   */
  public canExecute(): boolean {
    const now = Date.now();

    switch (this.state.state) {
      case 'CLOSED':
        return true;

      case 'OPEN':
        if (now - this.state.lastFailureTime > this.recoveryTimeout) {
          this.state.state = 'HALF_OPEN';
          console.log('Circuit breaker moving to HALF_OPEN state');
          return true;
        }
        return false;

      case 'HALF_OPEN':
        return true;

      default:
        return true;
    }
  }

  /**
   * Record a successful operation
   */
  public recordSuccess(): void {
    if (this.state.state === 'HALF_OPEN') {
      this.state.state = 'CLOSED';
      this.state.failures = 0;
      console.log('Circuit breaker reset to CLOSED state');
    } else if (this.state.state === 'CLOSED') {
      // Reset failure count after successful operations
      const now = Date.now();
      if (now - this.state.lastFailureTime > this.resetTimeout) {
        this.state.failures = 0;
      }
    }
  }

  /**
   * Record a failure
   */
  public recordFailure(error: Error): void {
    const now = Date.now();
    
    // Check if this is a connection-related error
    const isConnectionError = this.isConnectionError(error);
    
    if (isConnectionError) {
      this.state.failures++;
      this.state.lastFailureTime = now;

      if (this.state.failures >= this.failureThreshold) {
        this.state.state = 'OPEN';
        console.error(`Circuit breaker OPENED after ${this.state.failures} connection failures`);
      }
    }
  }

  /**
   * Check if error is connection-related
   */
  private isConnectionError(error: Error): boolean {
    const connectionErrorCodes = [
      '53300', // too_many_connections
      '08006', // connection_failure
      '08001', // sqlclient_unable_to_establish_sqlconnection
      '08003', // connection_does_not_exist
      '08004', // sqlserver_rejected_establishment_of_sqlconnection
    ];

    const errorMessage = error.message.toLowerCase();
    
    return connectionErrorCodes.some(code => error.message.includes(code)) ||
           errorMessage.includes('connection') ||
           errorMessage.includes('timeout') ||
           errorMessage.includes('pool');
  }

  /**
   * Get current circuit breaker status
   */
  public getStatus(): CircuitBreakerState & { canExecute: boolean } {
    return {
      ...this.state,
      canExecute: this.canExecute()
    };
  }
}

// Global circuit breaker instance
const dbCircuitBreaker = new DatabaseCircuitBreaker();

export { dbCircuitBreaker, DatabaseCircuitBreaker };

/**
 * Wrapper for database operations with circuit breaker
 */
export async function executeWithCircuitBreaker<T>(
  operation: () => Promise<T>,
  fallback?: () => Promise<T>
): Promise<T> {
  if (!dbCircuitBreaker.canExecute()) {
    console.warn('Circuit breaker is OPEN, database operation blocked');
    
    if (fallback) {
      return await fallback();
    }
    
    throw new Error('Database temporarily unavailable due to connection issues');
  }

  try {
    const result = await operation();
    dbCircuitBreaker.recordSuccess();
    return result;
  } catch (error) {
    dbCircuitBreaker.recordFailure(error as Error);
    throw error;
  }
}
