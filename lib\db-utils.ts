import { PoolClient } from 'pg';
import { getPool } from './db';

// Cache for column existence checks to avoid repeated queries
const columnExistsCache = new Map<string, boolean>();

/**
 * Checks if a column exists in a table with caching
 */
export async function columnExists(client: PoolClient, tableName: string, columnName: string): Promise<boolean> {
  const cacheKey = `${tableName}.${columnName}`;

  // Check cache first
  if (columnExistsCache.has(cacheKey)) {
    return columnExistsCache.get(cacheKey)!;
  }

  try {
    const result = await client.query(
      `SELECT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = $1
        AND column_name = $2
        AND table_schema = CURRENT_SCHEMA()
      ) AS "exists"`,
      [tableName, columnName]
    );

    const exists = result.rows[0].exists;
    // Cache the result for future use
    columnExistsCache.set(cacheKey, exists);
    return exists;
  } catch (error) {
    console.error(`Error checking if column ${columnName} exists in table ${tableName}:`, error);
    return false;
  }
}

/**
 * Gets a database connection from the pool
 */
export async function getDbClient(): Promise<{ client: PoolClient; releaseClient: () => void }> {
  const pool = getPool();
  const client = await pool.connect();
  return { 
    client, 
    releaseClient: () => client.release() 
  };
}

/**
 * Executes a database transaction with proper error handling and retry logic
 */
export async function executeTransaction<T>(
  callback: (client: PoolClient) => Promise<T>,
  maxRetries: number = 3
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const { client, releaseClient } = await getDbClient();

    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK').catch(() => {}); // Ignore rollback errors
      lastError = error as Error;

      // Check if error is retryable (serialization failure, deadlock, etc.)
      const isRetryable = isRetryableError(error as Error);

      if (attempt === maxRetries || !isRetryable) {
        console.error(`Transaction failed after ${attempt} attempts:`, error);
        throw error;
      }

      // Exponential backoff for retries
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      console.warn(`Transaction attempt ${attempt} failed, retrying in ${delay}ms:`, error);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      releaseClient();
    }
  }

  throw lastError!;
}

/**
 * Checks if a database error is retryable
 */
function isRetryableError(error: Error): boolean {
  const retryableCodes = [
    '40001', // serialization_failure
    '40P01', // deadlock_detected
    '53300', // too_many_connections
    '08006', // connection_failure
    '08001', // sqlclient_unable_to_establish_sqlconnection
  ];

  return retryableCodes.some(code => error.message.includes(code));
}

/**
 * Safely get user information, handling potential schema differences
 */
export async function getUserInfo(client: PoolClient, userId: string): Promise<{ 
  id: string; 
  name?: string; 
  email: string; 
  phone?: string;
}> {
  // Check which columns exist
  const hasNameColumn = await columnExists(client, 'users', 'name');
  const hasPhoneColumn = await columnExists(client, 'users', 'phone');
  
  // Build a dynamic query based on column existence
  const columns = ['id', 'email'];
  if (hasNameColumn) columns.push('name');
  if (hasPhoneColumn) columns.push('phone');
  
  const query = `SELECT ${columns.join(', ')} FROM users WHERE id = $1`;
  const result = await client.query(query, [userId]);
  
  if (result.rows.length === 0) {
    throw new Error('User not found');
  }
  
  return result.rows[0];
}

/**
 * Executes a query with automatic retry logic for connection issues
 */
export async function executeQuery<T = unknown>(
  query: string,
  params: unknown[] = [],
  maxRetries: number = 3
): Promise<{ rows: T[]; rowCount: number }> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const pool = getPool();
      const result = await pool.query(query, params);
      return {
        rows: result.rows,
        rowCount: result.rowCount || 0
      };
    } catch (error) {
      lastError = error as Error;

      const isRetryable = isRetryableError(error as Error);

      if (attempt === maxRetries || !isRetryable) {
        console.error(`Query failed after ${attempt} attempts:`, error);
        throw error;
      }

      // Exponential backoff for retries
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
      console.warn(`Query attempt ${attempt} failed, retrying in ${delay}ms:`, error);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * Batch insert with conflict resolution
 */
export async function batchInsert<T>(
  tableName: string,
  columns: string[],
  values: T[][],
  conflictResolution: 'ignore' | 'update' | 'error' = 'error',
  conflictColumns?: string[]
): Promise<void> {
  if (values.length === 0) return;

  const placeholders = values.map((_, rowIndex) =>
    `(${columns.map((_, colIndex) => `$${rowIndex * columns.length + colIndex + 1}`).join(', ')})`
  ).join(', ');

  const flatValues = values.flat();

  let query = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES ${placeholders}`;

  if (conflictResolution === 'ignore') {
    query += ' ON CONFLICT DO NOTHING';
  } else if (conflictResolution === 'update' && conflictColumns) {
    const updateSet = columns
      .filter(col => !conflictColumns.includes(col))
      .map(col => `${col} = EXCLUDED.${col}`)
      .join(', ');
    query += ` ON CONFLICT (${conflictColumns.join(', ')}) DO UPDATE SET ${updateSet}`;
  }

  await executeQuery(query, flatValues);
}

/**
 * Get table statistics for query optimization
 */
export async function getTableStats(tableName: string): Promise<{
  rowCount: number;
  tableSize: string;
  indexSize: string;
}> {
  interface TableStatsRow {
    table_size: string;
    index_size: string;
    row_count: number;
  }

  const sizeResult = await executeQuery<TableStatsRow>(`
    SELECT
      pg_size_pretty(pg_total_relation_size($1)) as table_size,
      pg_size_pretty(pg_indexes_size($1)) as index_size,
      (SELECT COUNT(*) FROM ${tableName}) as row_count
  `, [tableName]);

  const row = sizeResult.rows[0];
  return {
    rowCount: row?.row_count || 0,
    tableSize: row?.table_size || '0 bytes',
    indexSize: row?.index_size || '0 bytes'
  };
}