import { NextResponse } from 'next/server';
import { getPool } from "@/lib/db";

export async function POST(request: Request) {
  try {
    const data = await request.json();
    const {
      title,                // array
      shortDesc,            // array
      longDesc,             // array
      price,
      promotionalPricePercent,
      salesPricePercent,
      subcategoryId,
      imageUrl,
      galleryUrls,
      isFeatured           // <-- get from request
    } = data;

    // Ensure we're sending proper numeric values to PostgreSQL
    const numericPrice = typeof price === 'number' ? price : parseFloat(price) || 0;
    const numericPromotionalPercent = promotionalPricePercent === null ? null : 
      (typeof promotionalPricePercent === 'number' ? promotionalPricePercent : 
      (parseFloat(promotionalPricePercent) || null));
    const numericSalesPercent = salesPricePercent === null ? null : 
      (typeof salesPricePercent === 'number' ? salesPricePercent : 
      (parseFloat(salesPricePercent) || null));

    const pool = getPool();
    const result = await pool.query(
      `INSERT INTO product
        (title, short_description, long_description, price, promotional_price_percent, sales_price_percent, image_url, gallery_urls, subcategory_id, is_featured, created_at)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
       RETURNING *`,
      [
        title,                      // TEXT[]
        shortDesc,                  // TEXT[]
        longDesc,                   // TEXT[]
        numericPrice,               // NUMERIC
        numericPromotionalPercent,  // NUMERIC
        numericSalesPercent,        // NUMERIC
        imageUrl,                   // TEXT
        galleryUrls,                // TEXT[]
        subcategoryId,              // INTEGER
        isFeatured ?? false         // BOOLEAN, default to false if undefined
      ]
    );

    return NextResponse.json({ success: true, product: result.rows[0] }, { status: 200 });
  } catch (error) {
    console.error("Error adding product:", error);
    return NextResponse.json({ error: "Failed to add product" }, { status: 500 });
  }
}