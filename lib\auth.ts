import PostgresAdapter from "@auth/pg-adapter";
import NextAuth from "next-auth";
import Apple from "next-auth/providers/apple";
import Credentials from "next-auth/providers/credentials";
import Google from "next-auth/providers/google";
import { getUserFromDb } from "./auth-utils";
import { getPool, safeQuery } from "./db";
import { Adapter } from "next-auth/adapters";

export type UserType = "user" | "admin";

declare module "next-auth" {
  interface User {
    userType: UserType;
  }

  interface Session {
    user: {
      role:string
      id: string;
      name: string;
      email: string;
      userType: UserType;
    };
  }
}

// Get the pool once for the adapter - but only use it when necessary
const pool = getPool();

export const { handlers, auth, signIn, signOut } = NextAuth({
  // Use adapter only for OAuth providers, not for credentials
  adapter: PostgresAdapter(pool) as Adapter,
  session: {
    strategy: "jwt", // JWT strategy reduces database load
    maxAge: 24 * 60 * 60, // 24 hours session
  },
  callbacks: {
    async signIn({ user, account }) {
      if (account?.provider === "google") {
        try {
          // Use safeQuery to avoid connection management issues
          const result = await safeQuery<{ id: string; user_type: UserType }>(
            "SELECT id, user_type FROM users WHERE email = $1",
            [user.email]
          );
          if (result.rows.length > 0) {
            const existingUser = result.rows[0];
            user.id = existingUser.id;
            user.userType = existingUser.user_type;
          }
        } catch (error) {
          console.error("Error in signIn callback:", error);
          // Check if it's a connection error
          if (error && typeof error === 'object' && 'message' in error) {
            const errorMessage = (error as Error).message;
            if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
              console.error('Database connection limit reached in signIn callback');
            }
          }
          // Don't return false, just continue without userType
          // This prevents authentication failures due to database issues
        }
      }
      return true;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id as string;
        token.name = user.name as string;
        token.email = user.email as string;
        // Set userType from user object during login to avoid database queries
        token.userType = user.userType as UserType;
      }

      if (token.email && !token.userType) {
        try {
          // Use safeQuery to avoid connection management issues
          const result = await safeQuery<{ user_type: UserType }>(
            "SELECT user_type FROM users WHERE email = $1",
            [token.email as string]
          );
          if (result.rows.length > 0) {
            token.userType = result.rows[0].user_type;
          }
        } catch (error) {
          console.error('Error fetching user type in JWT callback:', error);
          // Check if it's a connection error
          if (error && typeof error === 'object' && 'message' in error) {
            const errorMessage = (error as Error).message;
            if (errorMessage.includes('53300') || errorMessage.includes('too_many_connections')) {
              console.error('Database connection limit reached in JWT callback');
            }
          }
          // Continue without userType to prevent auth failure
        }
      }

      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        session.user.userType = token.userType as UserType;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      else if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  providers: [
    Credentials({
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password required");
        }

        const user = await getUserFromDb(
          credentials.email as string,
          credentials.password as string
        );

        if (!user) {
          throw new Error("Invalid credentials.");
        }

        return {
          id: user.id,
          name: user.name,
          email: user.email,
          userType: user.userType,
        };
      },
    }),
    Google,
    Apple,
  ],
  debug: process.env.NODE_ENV === "development",
  pages: {
    signIn: "/login",
    error: "/login",
  },
});
