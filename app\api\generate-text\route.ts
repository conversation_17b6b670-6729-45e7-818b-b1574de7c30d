import { NextRequest, NextResponse } from 'next/server';

// Sample prompt suggestions based on product type
const promptSuggestions: Record<string, string[]> = {
  'tshirt': [
    'Wear it with pride.',
    'Style meets comfort.',
    'Express yourself.',
    'Made for the bold.',
    'Your style, your way.',
  ],
  'mug': [
    'Start your day right.',
    'Perfect for your morning ritual.',
    'Sip in style.',
    'For the coffee lover in you.',
    'Every sip tells a story.',
  ],
  'poster': [
    'Make a statement.',
    'Bring your walls to life.',
    'Art that speaks to you.',
    'Express your personality.',
    'Elevate your space.',
  ],
  'card': [
    'Share the love.',
    'Words that matter.',
    'Make someone smile.',
    'From the heart.',
    'Say it beautifully.',
  ],
  'hoodie': [
    'Stay warm, look cool.',
    'Comfort never goes out of style.',
    'Elevate your casual wear.',
    'For those who stand out.',
    'Embrace the cozy life.',
  ],
  'phonecase': [
    'Protect in style.',
    'More than just protection.',
    'Your phone, your style.',
    'Stand out with every call.',
    'Make a statement with every text.',
  ],
  'bag': [
    'Carry your world in style.',
    'Where function meets fashion.',
    'For life on the go.',
    'Designed for your journey.',
    'The perfect companion.',
  ],
};

// Default suggestions for unknown product types
const defaultSuggestions = [
  'Quality you can trust.',
  'Designed with you in mind.',
  'The perfect choice.',
  'Elevate your experience.',
  'Premium quality, exceptional value.',
];

export async function POST(request: NextRequest) {
  try {
    const { prompt, productId, productType, productName } = await request.json();
    
    // Simulate AI processing time
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // For demo purposes, choose from predefined text based on product type
    // In a real implementation, this would call an AI service like OpenAI
    let generatedText = '';
    
    // Add product name context if available
    const productContext = productName 
      ? `${productType || 'product'} "${productName}"`
      : productType || 'product';
    
    if (prompt.toLowerCase().includes('tagline') || prompt.toLowerCase().includes('slogan')) {
      // Choose from taglines/slogans
      const suggestions = productType 
        ? (promptSuggestions[productType.toLowerCase()] || defaultSuggestions)
        : defaultSuggestions;
      
      // If we have a product name, sometimes incorporate it
      if (productName && Math.random() > 0.5) {
        const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
        generatedText = `${productName}: ${randomSuggestion}`;
      } else {
        generatedText = suggestions[Math.floor(Math.random() * suggestions.length)];
      }
    } else if (prompt.toLowerCase().includes('description')) {
      // Generate a product description
      const attributes = [
        'premium', 'high-quality', 'exceptional', 'outstanding', 'superb'
      ];
      const benefits = [
        'designed for everyday use', 
        'perfect for any occasion', 
        'built to impress',
        'made with attention to detail',
        'crafted for those who demand the best'
      ];
      
      const attribute = attributes[Math.floor(Math.random() * attributes.length)];
      const benefit = benefits[Math.floor(Math.random() * benefits.length)];
      
      generatedText = `This ${attribute} ${productContext} is ${benefit}. It combines style and functionality in a way that truly stands out from the crowd.`;
    } else if (prompt.toLowerCase().includes('quote') || prompt.toLowerCase().includes('saying')) {
      // Generate inspirational quotes
      const quotes = [
        `"Life is too short to wear boring ${productType || 'things'}."`,
        `"Express yourself, be yourself, with our ${productContext}."`,
        `"Style isn't just what you wear, it's how you live."`,
        `"Be the best version of yourself."`,
        `"Dream big, live boldly."`,
      ];
      generatedText = quotes[Math.floor(Math.random() * quotes.length)];
    } else {
      // For other types of prompts, analyze the prompt content
      if (prompt.toLowerCase().includes('funny') || prompt.toLowerCase().includes('humor')) {
        generatedText = `Why so serious? Life's better with a ${productContext}!`;
      } else if (prompt.toLowerCase().includes('elegant') || prompt.toLowerCase().includes('sophisticated')) {
        generatedText = `Elegance is not standing out, but being remembered. Our ${productContext} ensures you'll never be forgotten.`;
      } else if (prompt.toLowerCase().includes('bold') || prompt.toLowerCase().includes('statement')) {
        generatedText = `Make a statement without saying a word with our ${productContext}.`;
      } else {
        // Generic response for other prompts
        generatedText = `Your custom ${productContext} represents your unique style and personality. Make it yours today!`;
      }
    }
    
    // Add product ID to response for tracking purposes
    return NextResponse.json({ 
      text: generatedText,
      productId,
      productType,
      productName
    });
  } catch (error) {
    console.error('Error in generate-text API:', error);
    return NextResponse.json(
      { error: 'Failed to generate text' },
      { status: 500 }
    );
  }
} 