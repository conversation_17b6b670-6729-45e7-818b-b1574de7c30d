"use client"

import { useAuth } from "@/hooks/useAuth"
import { canadianProvinces, type CanadianProvince } from "@/utils/states"
import { ArrowLeft, Palette } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useState, useEffect, Suspense, useMemo } from "react"
import { toast } from "sonner"
import { Button } from "@/components/ui/button"
import AddressAutocomplete from "@/components/address-autocomplete"
import { createOrder } from "../actions/order"
import { CheckoutSkeleton } from "@/components/CheckoutSkeleton"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface Product {
  id: number;
  title: string;
  short_description: string;
  price: string | number;
  image_url: string;
}

interface CustomDesign {
  id: string;
  productId: string;
  designData: string;
  createdAt: string;
}

function CheckoutContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const productId = searchParams.get("product")
  const designId = searchParams.get("designId")
  const queryDeliveryMethod = searchParams.get("deliveryMethod")
  const [deliveryMethod, setDeliveryMethod] = useState(queryDeliveryMethod || "PICKUP")
  const [pickupLocation, setPickupLocation] = useState("")
  const { isAuthenticated, status, session } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const [product, setProduct] = useState<Product | null>(null)
  const [customDesign, setCustomDesign] = useState<CustomDesign | null>(null)
  const [designMissing, setDesignMissing] = useState(false)
  const [formData, setFormData] = useState({
    street: "",
    city: "",
    province: "",
    postalCode: "",
  })

  // Parse pickup locations from environment variable using useMemo
  const pickupLocations = useMemo(() => {
    const pickupLocationsString = process.env.NEXT_PUBLIC_PICKUP_LOCATION || ""
    return pickupLocationsString ? pickupLocationsString.split('", "').map(loc => {
      // Clean up the string by removing extra quotes at the beginning and end
      return loc.replace(/^"/, '').replace(/"$/, '')
    }) : []
  }, [])

  useEffect(() => {
    // Set default pickup location if available
    if (pickupLocations.length > 0 && deliveryMethod === "PICKUP") {
      setPickupLocation(pickupLocations[0])
    }
  }, [deliveryMethod, pickupLocations])

  useEffect(() => {
    // Only redirect if authentication check is complete and user is not authenticated
    if (status !== "loading" && !isAuthenticated) {
      const returnUrl = encodeURIComponent(`/checkout?product=${productId}${designId ? `&designId=${designId}` : ''}`)
      router.push(`/login?returnUrl=${returnUrl}`)
    }
  }, [isAuthenticated, router, productId, designId, status])

  useEffect(() => {
    const fetchProduct = async () => {
      if (!productId) {
        toast.error("No product selected")
        router.push("/products")
        return
      }

      try {
        const response = await fetch(`/api/products/${productId}`)
        if (!response.ok) {
          throw new Error("Failed to fetch product")
        }
        const data = await response.json()
        setProduct(data)
      } catch (error) {
        console.error("Error fetching product:", error)
        toast.error("Failed to load product details")
        router.push("/products")
      }
    }

    if (isAuthenticated) {
      fetchProduct()
    }
  }, [productId, router, isAuthenticated])

  useEffect(() => {
    // Load custom design from localStorage if designId is provided
    if (designId) {
      try {
        const designs = JSON.parse(localStorage.getItem('productDesigns') || '{}')
        const design = designs[designId]
        if (design) {
          setCustomDesign(design)
          setDesignMissing(false)
        } else {
          setDesignMissing(true)
          toast.error("Custom design not found", {
            description: "Please create a design for this product"
          })
        }
      } catch (error) {
        console.error('Error loading custom design:', error)
        setDesignMissing(true)
      }
    } else {
      setDesignMissing(true)
    }
  }, [designId, productId])

  const handleAddressSelect = (address: {
    street: string
    city: string
    province: string
    postalCode: string
  }) => {
    setFormData(address)
  }

  const handleCreateDesign = () => {
    if (productId) {
      router.push(`/products/${productId}`)
    } else {
      router.push('/products')
    }
  }

  if (status === "loading") {
    return <CheckoutSkeleton />
  }

  if (!isAuthenticated) {
    return null
  }

  if (!product) {
    return <CheckoutSkeleton />
  }

  const subtotal = typeof product.price === 'string' ? parseFloat(product.price) : product.price
  const deliveryFeeAmount = 3.99
  const freeDeliveryThreshold = 75

  const envDeliveryFee = process.env.NEXT_PUBLIC_DELIVERY_FEE
    ? Number(process.env.NEXT_PUBLIC_DELIVERY_FEE)
    : deliveryFeeAmount

  const envFreeDeliveryThreshold = process.env.NEXT_PUBLIC_FREE_DELIVERY_THRESHOLD
    ? Number(process.env.NEXT_PUBLIC_FREE_DELIVERY_THRESHOLD)
    : freeDeliveryThreshold

  const deliveryFee =
    deliveryMethod === "DELIVERY" && subtotal < envFreeDeliveryThreshold
      ? envDeliveryFee
      : 0

  const promoDiscount = 0 // No promotional discount for single product checkout
  const totalPrice = subtotal + deliveryFee - promoDiscount

  const handleSubmit = async () => {
    try {
      if (designMissing) {
        toast.error("Custom design required", {
          description: "Please create a design for this product before placing your order"
        })
        return
      }

      setIsProcessing(true)

      if (!session?.user?.id) {
        throw new Error('User not authenticated')
      }

      if (
        deliveryMethod === 'DELIVERY' &&
        (!formData.street ||
          !formData.city ||
          !formData.province ||
          !formData.postalCode)
      ) {
        throw new Error('Please fill in all required address fields')
      }

      if (deliveryMethod === 'PICKUP' && !pickupLocation) {
        throw new Error('Please select a pickup location')
      }

      if (!customDesign) {
        throw new Error('Custom design is required to complete your order')
      }

      const specialInstructions = document.getElementById(
        'specialInstructions'
      ) as HTMLTextAreaElement

      const result = await createOrder({
        userId: session.user.id,
        items: [{
          id: product.id.toString(),
          name: product.title,
          quantity: 1,
          price: typeof product.price === 'string' ? parseFloat(product.price) : product.price,
          image: product.image_url,
          customDesignId: customDesign?.id,
          customDesignData: customDesign?.designData
        }],
        deliveryMethod: deliveryMethod as 'PICKUP' | 'DELIVERY',
        deliveryAddress: deliveryMethod === 'DELIVERY' ? {
          street: formData.street.trim(),
          city: formData.city.trim(),
          province: formData.province.trim(),
          postalCode: formData.postalCode.replace(/\s/g, '').toUpperCase()
        } : undefined,
        pickupLocation: deliveryMethod === 'PICKUP' ? pickupLocation : undefined,
        specialInstructions: specialInstructions?.value || '',
        subtotal,
        deliveryFee,
        promotionalDiscount: promoDiscount,
        totalAmount: totalPrice
      })

      if (result.success) {
        toast.success('Order placed successfully!')
        router.push(`/order-success?orderId=${result.orderId}`)
      } else {
        throw new Error('Failed to create order')
      }
    } catch (error) {
      console.error('Order submission error:', error)
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to place order. Please try again.'
      )
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 mt-[120px]">
      {isProcessing && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="flex flex-col items-center">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: "0s" }}></div>
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: "0.2s" }}></div>
                <div className="w-3 h-3 rounded-full bg-blue-600 animate-bounce" style={{ animationDelay: "0.4s" }}></div>
              </div>
              <h3 className="text-xl font-semibold mb-2">Processing Order</h3>
              <p className="text-gray-600 text-center">
                Please wait while we process your order...
              </p>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-4xl mx-auto">
        <div className="flex items-center gap-2 mb-8">
          <Link prefetch={true} href={`/products/${productId}`}>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-600 hover:text-blue-600"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Product
            </Button>
          </Link>
        </div>

        <h1 className="text-2xl md:text-3xl font-bold mb-8">Checkout</h1>

        {designMissing && (
          <Alert variant="destructive" className="mb-6">
            <Palette className="h-4 w-4" />
            <AlertTitle>Custom Design Required</AlertTitle>
            <AlertDescription className="flex flex-col gap-3">
              <p>You need to create a custom design for this product before checkout.</p>
              <Button 
                onClick={handleCreateDesign}
                variant="outline"
                className="w-fit"
              >
                Create Your Design
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Order Summary */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Order Summary</h2>
            <div className="space-y-4">
              <div className="flex items-center gap-4">
                <div className="relative w-16 h-16">
                  <Image
                    src={product.image_url}
                    alt={product.title}
                    fill
                    className="object-cover rounded-lg"
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium">{product.title}</h3>
                  <p className="text-sm text-gray-600">Qty: 1</p>
                  <p className="text-blue-600 font-medium">
                    ${(typeof product.price === 'string' ? parseFloat(product.price) : product.price).toFixed(2)}
                  </p>
                </div>
              </div>

              {/* Custom Design Preview */}
              {customDesign ? (
                <div className="mt-4 border-t pt-4">
                  <h3 className="font-medium mb-2">Your Custom Design</h3>
                  <div className="relative w-full h-[200px] border border-gray-200 rounded-lg overflow-hidden">
                    <Image
                      src={customDesign.designData}
                      alt="Your Custom Design"
                      fill
                      className="object-contain"
                    />
                  </div>
                </div>
              ) : (
                <div className="mt-4 border-t pt-4">
                  <h3 className="font-medium mb-2 text-red-500">Custom Design Missing</h3>
                  <div className="bg-gray-100 p-4 rounded-lg flex flex-col items-center justify-center h-[200px]">
                    <Palette className="h-8 w-8 text-gray-400 mb-2" />
                    <p className="text-gray-500 text-center">No custom design found</p>
                    <Button 
                      onClick={handleCreateDesign}
                      variant="outline"
                      size="sm"
                      className="mt-4"
                    >
                      Create Your Design
                    </Button>
                  </div>
                </div>
              )}

              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between text-gray-600">
                  <span>Subtotal</span>
                  <span>${subtotal.toFixed(2)}</span>
                </div>

                {deliveryMethod === "DELIVERY" && (
                  <div className="flex justify-between text-gray-600">
                    <span>Delivery Fee</span>
                    {subtotal < envFreeDeliveryThreshold ? (
                      <span>${envDeliveryFee.toFixed(2)}</span>
                    ) : (
                      <span className="text-green-600">Free</span>
                    )}
                  </div>
                )}

                <div className="border-t pt-4">
                  <div className="flex justify-between font-bold text-lg">
                    <span>Total</span>
                    <span className="text-blue-600">
                      ${totalPrice.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Delivery Form */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-6">Delivery Information</h2>
            <div className="space-y-6">
              <div className="flex gap-4">
                <Button
                  variant={deliveryMethod === "PICKUP" ? "default" : "outline"}
                  className="flex-1"
                  onClick={() => setDeliveryMethod("PICKUP")}
                >
                  Pickup
                </Button>
                <Button
                  variant={deliveryMethod === "DELIVERY" ? "default" : "outline"}
                  className="flex-1"
                  onClick={() => setDeliveryMethod("DELIVERY")}
                >
                  Delivery
                </Button>
              </div>

              {deliveryMethod === "DELIVERY" && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Street Address
                    </label>
                    <AddressAutocomplete onAddressSelect={handleAddressSelect} />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        City
                      </label>
                      <input
                        type="text"
                        value={formData.city}
                        onChange={(e) =>
                          setFormData({ ...formData, city: e.target.value })
                        }
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Province
                      </label>
                      <select
                        value={formData.province}
                        onChange={(e) =>
                          setFormData({ ...formData, province: e.target.value })
                        }
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                        required
                      >
                        <option value="">Select Province</option>
                        {canadianProvinces.map((province: CanadianProvince) => (
                          <option key={province.code} value={province.code}>
                            {province.name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Postal Code
                    </label>
                    <input
                      type="text"
                      value={formData.postalCode}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          postalCode: e.target.value.toUpperCase(),
                        })
                      }
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                      required
                      pattern="[A-Za-z][0-9][A-Za-z] [0-9][A-Za-z][0-9]"
                      placeholder="A1A 1A1"
                    />
                  </div>
                </div>
              )}

              {deliveryMethod === "PICKUP" && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Pickup Location
                    </label>
                    <select
                      value={pickupLocation}
                      onChange={(e) => setPickupLocation(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                      required
                    >
                      <option value="">Select Pickup Location</option>
                      {pickupLocations.map((location, index) => (
                        <option key={index} value={location}>
                          {location}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              )}

              <div>
                <label
                  htmlFor="specialInstructions"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  Special Instructions{" "}
                  <span className="text-gray-500 text-xs">(Optional)</span>
                </label>
                <textarea
                  id="specialInstructions"
                  name="specialInstructions"
                  rows={3}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-600 focus:border-transparent"
                  placeholder="Add any special instructions for your order here"
                />
              </div>

              <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
                <p>
                  <span className="font-medium">Order Confirmation:</span> You will receive an order confirmation email at {session?.user?.email} once your order is placed.
                </p>
              </div>

              <Button
                onClick={handleSubmit}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                disabled={isProcessing || designMissing}
              >
                {isProcessing ? "Processing..." : "Place Order"}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function CheckoutPage() {
  return (
    <Suspense
      fallback={<CheckoutSkeleton />}
    >
      <CheckoutContent />
    </Suspense>
  )
} 