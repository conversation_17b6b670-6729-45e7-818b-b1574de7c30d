import { getPool } from './db';

/**
 * Connection monitoring and debugging utilities
 */

export interface ConnectionPoolStats {
  totalCount: number;
  idleCount: number;
  waitingCount: number;
  maxConnections: number;
  utilizationPercent: number;
}

export interface DatabaseConnectionInfo {
  activeConnections: number;
  idleConnections: number;
  totalConnections: number;
  maxConnections: number;
  connectionsByState: Record<string, number>;
}

/**
 * Get current pool statistics
 */
export function getPoolStats(): ConnectionPoolStats {
  const pool = getPool();
  
  const stats = {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount,
    maxConnections: 5, // Our configured max
    utilizationPercent: 0
  };
  
  stats.utilizationPercent = Math.round((stats.totalCount / stats.maxConnections) * 100);
  
  return stats;
}

/**
 * Get database connection information from PostgreSQL
 */
export async function getDatabaseConnectionInfo(): Promise<DatabaseConnectionInfo> {
  const pool = getPool();
  
  try {
    const result = await pool.query(`
      SELECT 
        state,
        COUNT(*) as count,
        (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_conn
      FROM pg_stat_activity 
      WHERE datname = current_database()
      GROUP BY state, max_conn
    `);

    const connectionsByState: Record<string, number> = {};
    let activeConnections = 0;
    let idleConnections = 0;
    let totalConnections = 0;
    let maxConnections = 100; // default

    result.rows.forEach(row => {
      connectionsByState[row.state] = row.count;
      totalConnections += row.count;
      
      if (row.state === 'active') {
        activeConnections = row.count;
      } else if (row.state === 'idle') {
        idleConnections = row.count;
      }
      
      maxConnections = row.max_conn;
    });

    return {
      activeConnections,
      idleConnections,
      totalConnections,
      maxConnections,
      connectionsByState
    };
  } catch (error) {
    console.error('Error getting database connection info:', error);
    return {
      activeConnections: 0,
      idleConnections: 0,
      totalConnections: 0,
      maxConnections: 0,
      connectionsByState: {}
    };
  }
}

/**
 * Monitor connections and log warnings
 */
export async function monitorConnections(): Promise<void> {
  try {
    const poolStats = getPoolStats();
    const dbInfo = await getDatabaseConnectionInfo();
    
    console.log('=== Connection Monitor ===');
    console.log(`Pool Stats: ${poolStats.totalCount}/${poolStats.maxConnections} (${poolStats.utilizationPercent}%)`);
    console.log(`  - Idle: ${poolStats.idleCount}, Waiting: ${poolStats.waitingCount}`);
    console.log(`Database: ${dbInfo.totalConnections}/${dbInfo.maxConnections} connections`);
    console.log(`  - Active: ${dbInfo.activeConnections}, Idle: ${dbInfo.idleConnections}`);
    
    // Log connection states
    if (Object.keys(dbInfo.connectionsByState).length > 0) {
      console.log('Connection states:', dbInfo.connectionsByState);
    }
    
    // Warnings
    if (poolStats.utilizationPercent > 80) {
      console.warn('⚠️  Pool utilization high:', poolStats.utilizationPercent + '%');
    }
    
    if (dbInfo.totalConnections > dbInfo.maxConnections * 0.8) {
      console.warn('⚠️  Database connection usage high:', 
        `${dbInfo.totalConnections}/${dbInfo.maxConnections}`);
    }
    
    if (poolStats.waitingCount > 0) {
      console.warn('⚠️  Clients waiting for connections:', poolStats.waitingCount);
    }
    
    console.log('========================\n');
  } catch (error) {
    console.error('Error monitoring connections:', error);
  }
}

/**
 * Start periodic connection monitoring
 */
export function startConnectionMonitoring(intervalMs: number = 30000): NodeJS.Timeout {
  console.log('Starting connection monitoring...');
  
  // Initial check
  monitorConnections();
  
  // Periodic checks
  return setInterval(() => {
    monitorConnections();
  }, intervalMs);
}

/**
 * Check for potential connection leaks
 */
export async function checkForConnectionLeaks(): Promise<{
  hasLeaks: boolean;
  longRunningQueries: Array<{
    pid: number;
    duration: string;
    query: string;
    state: string;
  }>;
}> {
  const pool = getPool();
  
  try {
    const result = await pool.query(`
      SELECT 
        pid,
        now() - pg_stat_activity.query_start AS duration,
        query,
        state
      FROM pg_stat_activity 
      WHERE datname = current_database()
        AND state != 'idle'
        AND now() - pg_stat_activity.query_start > interval '30 seconds'
      ORDER BY duration DESC
    `);

    const longRunningQueries = result.rows.map(row => ({
      pid: row.pid,
      duration: row.duration,
      query: row.query?.substring(0, 100) + '...',
      state: row.state
    }));

    return {
      hasLeaks: longRunningQueries.length > 0,
      longRunningQueries
    };
  } catch (error) {
    console.error('Error checking for connection leaks:', error);
    return {
      hasLeaks: false,
      longRunningQueries: []
    };
  }
}

/**
 * Emergency connection cleanup
 */
export async function emergencyCleanup(): Promise<void> {
  console.log('🚨 Performing emergency connection cleanup...');
  
  try {
    const leakCheck = await checkForConnectionLeaks();
    
    if (leakCheck.hasLeaks) {
      console.log('Found long-running queries:');
      leakCheck.longRunningQueries.forEach((query, index) => {
        console.log(`${index + 1}. PID ${query.pid} (${query.duration}): ${query.query}`);
      });
    }
    
    // Log current stats
    await monitorConnections();
    
    // Force pool recreation if needed
    const poolStats = getPoolStats();
    if (poolStats.utilizationPercent > 90) {
      console.log('Pool utilization critical, consider restarting application');
    }
    
  } catch (error) {
    console.error('Error during emergency cleanup:', error);
  }
}

/**
 * Get connection health status
 */
export async function getConnectionHealth(): Promise<{
  status: 'healthy' | 'warning' | 'critical';
  issues: string[];
  recommendations: string[];
}> {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  try {
    const poolStats = getPoolStats();
    const dbInfo = await getDatabaseConnectionInfo();
    const leakCheck = await checkForConnectionLeaks();
    
    // Check pool utilization
    if (poolStats.utilizationPercent > 90) {
      issues.push(`Pool utilization critical: ${poolStats.utilizationPercent}%`);
      recommendations.push('Increase pool size or reduce concurrent operations');
    } else if (poolStats.utilizationPercent > 70) {
      issues.push(`Pool utilization high: ${poolStats.utilizationPercent}%`);
      recommendations.push('Monitor for potential connection leaks');
    }
    
    // Check database connections
    const dbUtilization = Math.round((dbInfo.totalConnections / dbInfo.maxConnections) * 100);
    if (dbUtilization > 90) {
      issues.push(`Database connection usage critical: ${dbUtilization}%`);
      recommendations.push('Review all applications using this database');
    }
    
    // Check for waiting clients
    if (poolStats.waitingCount > 0) {
      issues.push(`${poolStats.waitingCount} clients waiting for connections`);
      recommendations.push('Optimize query performance or increase pool size');
    }
    
    // Check for long-running queries
    if (leakCheck.hasLeaks) {
      issues.push(`${leakCheck.longRunningQueries.length} long-running queries detected`);
      recommendations.push('Review and optimize slow queries');
    }
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (issues.some(issue => issue.includes('critical'))) {
      status = 'critical';
    } else if (issues.length > 0) {
      status = 'warning';
    }
    
    return { status, issues, recommendations };
  } catch (error) {
    console.error('Error checking connection health:', error);
    return {
      status: 'critical',
      issues: ['Unable to check connection health'],
      recommendations: ['Check database connectivity']
    };
  }
}
