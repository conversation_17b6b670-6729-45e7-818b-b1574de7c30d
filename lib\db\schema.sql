CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    delivery_method VARCHAR(20) NOT NULL,
    delivery_fee DECIMAL(10,2) DEFAULT 0,
    promotional_discount DECIMAL(10,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'PENDING',
    special_instructions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    product_id VARCHAR(50) NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL,
    price_at_order DECIMAL(10,2) NOT NULL,
    item_subtotal DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS delivery_addresses (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id) ON DELETE CASCADE,
    street VARCHAR(255) NOT NULL,
    city VARCHAR(100) NOT NULL,
    province VARCHAR(100) NOT NULL,
    postal_code VARCHAR(10) NOT NULL,
    country VARCHAR(100) DEFAULT 'Canada',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);


CREATE TABLE category (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL
);


CREATE TABLE subcategory (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  category_id INTEGER NOT NULL,
  FOREIGN KEY (category_id) REFERENCES category(id) ON DELETE CASCADE
);


CREATE TABLE product (
  id SERIAL PRIMARY KEY,
  title TEXT[] NOT NULL,
  short_description TEXT[],
  long_description TEXT[],
  price NUMERIC(10, 2),
  promotional_price_percent NUMERIC(5, 2),
  sales_price_percent NUMERIC(5, 2),
  image_url TEXT,
  gallery_urls TEXT[],
  subcategory_id INTEGER,
  created_at TIMESTAMPTZ DEFAULT (NOW() AT TIME ZONE 'America/Toronto'),
  updated_at TIMESTAMPTZ DEFAULT (NOW() AT TIME ZONE 'America/Toronto'),
  FOREIGN KEY (subcategory_id) REFERENCES subcategory(id) ON DELETE SET NULL
);


-- Optimized indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_user_status ON orders(user_id, status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_delivery_addresses_order_id ON delivery_addresses(order_id);


-- Optimized product indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_title_gin ON product USING gin (title);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_short_description_gin ON product USING gin (short_description);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_long_description_gin ON product USING gin (long_description);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_price ON product(price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_subcategory ON product(subcategory_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_created_at ON product(created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_is_featured ON product(is_featured) WHERE is_featured = true;

-- Composite indexes for common query patterns
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_subcategory_price ON product(subcategory_id, price);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_subcategory_created ON product(subcategory_id, created_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_product_featured_created ON product(is_featured, created_at DESC) WHERE is_featured = true;