"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { useAuth } from "@/hooks/useAuth"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle2, Clock, MapPin, Truck } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { OrderSuccessSkeleton } from "@/components/OrderSuccessSkeleton"

interface OrderItem {
  id: string
  name: string
  quantity: number
  price: number
  image: string
  customDesignId?: string
}

interface DeliveryAddress {
  street: string
  city: string
  province: string
  postalCode: string
}

interface OrderDetails {
  orderNumber: string
  status: string
  items: OrderItem[]
  subtotal: number
  deliveryFee: number
  promotionalDiscount: number
  totalAmount: number
  deliveryMethod: "PICKUP" | "DELIVERY"
  deliveryAddress?: DeliveryAddress
  specialInstructions?: string
  createdAt: string
  estimatedDeliveryTime?: string
  customDesignImageUrl?: string | null
}

function OrderSuccessContent() {
  const searchParams = useSearchParams()
  const orderId = searchParams.get("orderId")
  const { isAuthenticated } = useAuth()
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId) {
        setError("Order ID not found")
        setIsLoading(false)
        return
      }

      try {
        const response = await fetch(`/api/orders/${orderId}`)
        if (!response.ok) {
          throw new Error("Failed to fetch order details")
        }
        const data = await response.json()
        setOrderDetails(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load order details")
      } finally {
        setIsLoading(false)
      }
    }

    if (isAuthenticated) {
      fetchOrderDetails()
    }
  }, [orderId, isAuthenticated])

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8 mt-[120px] text-center">
        <h1 className="text-2xl font-bold mb-4">Please sign in to view your order</h1>
        <Link href="/login" prefetch={true}>
          <Button>Sign In</Button>
        </Link>
      </div>
    )
  }

  if (isLoading) {
    return <OrderSuccessSkeleton />
  }

  if (error || !orderDetails) {
    return (
      <div className="container mx-auto px-4 py-8 mt-[120px] text-center">
        <h1 className="text-2xl font-bold mb-4 text-red-600">Error</h1>
        <p className="mb-4">{error || "Order not found"}</p>
        <Link href="/" prefetch={true}>
          <Button>Return to Home</Button>
        </Link>
      </div>
    )
  }
  

  return (
    <div className="container mx-auto px-4 py-8 mt-[120px]">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">Order Confirmed!</h1>
              <p className="text-gray-600">Order #{orderDetails.orderNumber}</p>
            </div>
            <div className="flex items-center text-green-600">
              <CheckCircle2 className="w-6 h-6 mr-2" />
              <span className="font-medium">Confirmed</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="flex items-start">
              <Clock className="w-5 h-5 mr-3 text-gray-600 mt-1" />
              <div>
                <h3 className="font-medium">Order Placed</h3>
                <p className="text-gray-600">
                  {new Date(orderDetails.createdAt).toLocaleString()}
                </p>
              </div>
            </div>

            <div className="flex items-start">
              {orderDetails.deliveryMethod === "DELIVERY" ? (
                <>
                  <Truck className="w-5 h-5 mr-3 text-gray-600 mt-1" />
                  <div>
                    <h3 className="font-medium">Delivery</h3>
                    <p className="text-gray-600">
                      Estimated delivery: {orderDetails.estimatedDeliveryTime}
                    </p>
                  </div>
                </>
              ) : (
                <>
                  <MapPin className="w-5 h-5 mr-3 text-gray-600 mt-1" />
                  <div>
                    <h3 className="font-medium">Pickup</h3>
                    <p className="text-gray-600">
                      Ready in approximately 25-30 minutes
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          {orderDetails.deliveryMethod === "DELIVERY" && orderDetails.deliveryAddress && (
            <div className="mb-6">
              <h3 className="font-medium mb-2">Delivery Address</h3>
              <p className="text-gray-600">
                {orderDetails.deliveryAddress.street}
                <br />
                {orderDetails.deliveryAddress.city}, {orderDetails.deliveryAddress.province}
                <br />
                {orderDetails.deliveryAddress.postalCode}
              </p>
            </div>
          )}

          {orderDetails.specialInstructions && (
            <div className="mb-6">
              <h3 className="font-medium mb-2">Special Instructions</h3>
              <p className="text-gray-600">{orderDetails.specialInstructions}</p>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold mb-6">Order Summary</h2>
          <div className="space-y-4">
            {orderDetails.items.map((item) => (
              <div key={item.id} className="flex flex-col gap-4">
                <div className="flex items-center gap-4">
                  <div className="relative w-16 h-16">
                    <Image
                      src={item.image || "/placeholder.svg"}
                      alt={item.name || "Product image"}
                      fill
                      className="object-cover rounded-lg"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium">{item.name}</h3>
                    <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                    <p className="text-blue-600 font-medium">
                      ${((parseFloat(String(item.price)) || 0) * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
                
                {item.customDesignId && orderDetails.customDesignImageUrl && (
                  <div className="mt-2 pl-20">
                    <p className="text-sm font-medium text-gray-700 mb-1">Custom Design Included</p>
                    <div className="relative w-full h-[150px] border border-gray-200 rounded-lg overflow-hidden">
                      <Image
                        src={orderDetails.customDesignImageUrl}
                        alt="Custom Design"
                        fill
                        className="object-contain"
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}

            <div className="border-t pt-4 space-y-2">
              <div className="flex justify-between text-gray-600">
                <span>Subtotal</span>
                <span>${(parseFloat(String(orderDetails.subtotal).replace(/[^0-9.]/g, '')) || 0).toFixed(2)}</span>
              </div>

              {orderDetails.deliveryMethod === "DELIVERY" && (
                <div className="flex justify-between text-gray-600">
                  <span>Delivery Fee</span>
                  <span>${(parseFloat(String(orderDetails.deliveryFee).replace(/[^0-9.]/g, '')) || 0).toFixed(2)}</span>
                </div>
              )}

              {orderDetails.promotionalDiscount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Promotional Discount</span>
                  <span>-${(parseFloat(String(orderDetails.promotionalDiscount).replace(/[^0-9.]/g, '')) || 0).toFixed(2)}</span>
                </div>
              )}

              <div className="flex justify-between font-bold text-lg pt-2 border-t">
                <span>Total</span>
                <span className="text-blue-600">
                  ${(parseFloat(String(orderDetails.totalAmount).replace(/[^0-9.]/g, '')) || 0).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {orderDetails.customDesignImageUrl && (
          <div className="mt-4 border-t pt-4">
            <h3 className="font-medium mb-2">Your Custom Design</h3>
            <div className="relative w-full h-[200px] border border-gray-200 rounded-lg overflow-hidden">
              <Image
                src={orderDetails.customDesignImageUrl}
                alt="Custom Design"
                fill
                className="object-contain"
              />
            </div>
          </div>
        )}

        <div className="mt-8 flex justify-center gap-4">
          <Link href="/" prefetch={true}>
            <Button variant="outline">Continue Shopping</Button>
          </Link>
          <Link href="/orders" prefetch={true}>
            <Button>View All Orders</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}

export default function OrderSuccessPage() {
  return (
    <Suspense fallback={<OrderSuccessSkeleton />}>
      <OrderSuccessContent />
    </Suspense>
  )
}